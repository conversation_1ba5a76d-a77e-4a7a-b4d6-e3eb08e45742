<?php
/**
 * Admin Layout Template
 * 
 * Base layout for all admin interface pages.
 * Provides consistent navigation, header, and footer.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Extract variables for use in template
$pageTitle = $pageTitle ?? 'Admin Dashboard';
$user = $user ?? null;
$currentPage = $currentPage ?? 'dashboard';
$sessionTimeRemaining = $sessionTimeRemaining ?? 0;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?> - GuardGeo API Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3498db;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 0.9rem;
        }

        .user-email {
            color: #ecf0f1;
        }

        .user-role {
            background: #3498db;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            text-transform: uppercase;
        }

        .session-timer {
            color: #f39c12;
            font-size: 0.8rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            min-height: calc(100vh - 80px);
        }

        .sidebar {
            width: 250px;
            background: white;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            padding: 2rem 0;
        }

        .nav {
            list-style: none;
        }

        .nav-item {
            margin: 0;
        }

        .nav-link {
            display: block;
            padding: 1rem 2rem;
            color: #333;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: #f8f9fa;
            border-left-color: #3498db;
        }

        .nav-link.active {
            background: #e3f2fd;
            border-left-color: #3498db;
            color: #2980b9;
            font-weight: 500;
        }

        .nav-link.logout {
            color: #e74c3c;
            margin-top: 2rem;
            border-top: 1px solid #eee;
            padding-top: 2rem;
        }

        .nav-link.logout:hover {
            background: #fdf2f2;
            border-left-color: #e74c3c;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            background: white;
            margin: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .page-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .page-description {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 4px;
            border-left: 4px solid;
        }

        .alert-success {
            background: #d4edda;
            border-color: #27ae60;
            color: #155724;
        }

        .alert-error {
            background: #f8d7da;
            border-color: #e74c3c;
            color: #721c24;
        }

        .alert-warning {
            background: #fff3cd;
            border-color: #f39c12;
            color: #856404;
        }

        .alert-info {
            background: #d1ecf1;
            border-color: #3498db;
            color: #0c5460;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s ease;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .footer {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
            font-size: 0.9rem;
            border-top: 1px solid #eee;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 1rem 0;
            }

            .nav {
                display: flex;
                overflow-x: auto;
            }

            .nav-link {
                white-space: nowrap;
                padding: 0.75rem 1rem;
                border-left: none;
                border-bottom: 3px solid transparent;
            }

            .nav-link.active {
                border-left: none;
                border-bottom-color: #3498db;
            }

            .main-content {
                margin: 0;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">GuardGeo API Platform</div>
            <?php if ($user): ?>
            <div class="user-info">
                <span class="user-email"><?= htmlspecialchars($user['email']) ?></span>
                <span class="user-role"><?= htmlspecialchars($user['role']) ?></span>
                <?php if ($sessionTimeRemaining > 0): ?>
                <span class="session-timer" id="session-timer" data-remaining="<?= $sessionTimeRemaining ?>">
                    Session: <?= gmdate('H:i:s', $sessionTimeRemaining) ?>
                </span>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </header>

    <div class="container">
        <nav class="sidebar">
            <ul class="nav">
                <li class="nav-item">
                    <a href="/admin" class="nav-link <?= $currentPage === 'dashboard' ? 'active' : '' ?>">
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/logs" class="nav-link <?= $currentPage === 'logs' ? 'active' : '' ?>">
                        View Logs
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/ip-lookup" class="nav-link <?= $currentPage === 'ip-lookup' ? 'active' : '' ?>">
                        IP Lookup
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/queue" class="nav-link <?= $currentPage === 'queue' ? 'active' : '' ?>">
                        Queue Management
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/sync" class="nav-link <?= $currentPage === 'sync' ? 'active' : '' ?>">
                        Sync Tools
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/logout" class="nav-link logout">
                        Logout
                    </a>
                </li>
            </ul>
        </nav>

        <main class="main-content">
            <?php if (isset($content)): ?>
                <?= $content ?>
            <?php endif; ?>
        </main>
    </div>

    <footer class="footer">
        <p>&copy; <?= date('Y') ?> GuardGeo API Platform. All rights reserved.</p>
    </footer>

    <script>
        // Session timer countdown
        const sessionTimer = document.getElementById('session-timer');
        if (sessionTimer) {
            let remaining = parseInt(sessionTimer.dataset.remaining);
            
            const updateTimer = () => {
                if (remaining <= 0) {
                    sessionTimer.textContent = 'Session expired';
                    sessionTimer.style.color = '#e74c3c';
                    // Redirect to login after 5 seconds
                    setTimeout(() => {
                        window.location.href = '/admin/login?expired=1';
                    }, 5000);
                    return;
                }
                
                const hours = Math.floor(remaining / 3600);
                const minutes = Math.floor((remaining % 3600) / 60);
                const seconds = remaining % 60;
                
                sessionTimer.textContent = `Session: ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                if (remaining <= 300) { // 5 minutes warning
                    sessionTimer.style.color = '#e74c3c';
                }
                
                remaining--;
            };
            
            updateTimer();
            setInterval(updateTimer, 1000);
        }
    </script>
</body>
</html>
