<?php

namespace Skpassegna\GuardgeoApi\Core;

/**
 * ResponseFormatter Class
 * 
 * Provides consistent JSON response formatting for the GuardGeo API Platform.
 * Handles success responses, error responses, and HTTP status codes.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class ResponseFormatter
{
    /**
     * Send a successful JSON response
     * 
     * @param mixed $data Response data
     * @param int $statusCode HTTP status code (default: 200)
     * @param array $meta Additional metadata
     * @return void
     */
    public static function success($data = null, int $statusCode = 200, array $meta = []): void
    {
        $response = [
            'success' => true,
            'data' => $data
        ];
        
        if (!empty($meta)) {
            $response['meta'] = $meta;
        }
        
        self::sendJsonResponse($response, $statusCode);
    }

    /**
     * Send an error JSON response
     * 
     * @param string $message Error message
     * @param int $statusCode HTTP status code (default: 400)
     * @param string|null $code Error code
     * @param string|null $details Additional error details
     * @param string|null $requestId Request identifier for tracking
     * @return void
     */
    public static function error(
        string $message, 
        int $statusCode = 400, 
        ?string $code = null, 
        ?string $details = null,
        ?string $requestId = null
    ): void {
        $error = [
            'message' => $message
        ];
        
        if ($code !== null) {
            $error['code'] = $code;
        }
        
        if ($details !== null) {
            $error['details'] = $details;
        }
        
        $response = [
            'success' => false,
            'error' => $error
        ];
        
        if ($requestId !== null) {
            $response['request_id'] = $requestId;
        }
        
        self::sendJsonResponse($response, $statusCode);
    }

    /**
     * Send a validation error response
     * 
     * @param array $errors Validation errors (field => message)
     * @param string $message General error message
     * @return void
     */
    public static function validationError(array $errors, string $message = 'Validation failed'): void
    {
        $response = [
            'success' => false,
            'error' => [
                'code' => 'VALIDATION_ERROR',
                'message' => $message,
                'details' => $errors
            ]
        ];
        
        self::sendJsonResponse($response, 422);
    }

    /**
     * Send an authentication error response
     * 
     * @param string $message Error message
     * @return void
     */
    public static function authenticationError(string $message = 'Authentication required'): void
    {
        self::error($message, 401, 'AUTHENTICATION_ERROR');
    }

    /**
     * Send an authorization error response
     * 
     * @param string $message Error message
     * @return void
     */
    public static function authorizationError(string $message = 'Access denied'): void
    {
        self::error($message, 403, 'AUTHORIZATION_ERROR');
    }

    /**
     * Send a not found error response
     * 
     * @param string $message Error message
     * @return void
     */
    public static function notFound(string $message = 'Resource not found'): void
    {
        self::error($message, 404, 'NOT_FOUND');
    }

    /**
     * Send a rate limit error response
     * 
     * @param string $message Error message
     * @param int $retryAfter Seconds to wait before retrying
     * @return void
     */
    public static function rateLimitError(string $message = 'Rate limit exceeded', int $retryAfter = 60): void
    {
        header("Retry-After: $retryAfter");
        self::error($message, 429, 'RATE_LIMIT_EXCEEDED');
    }

    /**
     * Send an internal server error response
     * 
     * @param string $message Error message
     * @param string|null $details Additional details (only in debug mode)
     * @return void
     */
    public static function serverError(string $message = 'Internal server error', ?string $details = null): void
    {
        // Only include details in debug mode
        $includeDetails = defined('DEBUG') && DEBUG === true;
        
        self::error(
            $message, 
            500, 
            'INTERNAL_SERVER_ERROR',
            $includeDetails ? $details : null
        );
    }

    /**
     * Send JSON response with proper headers
     * 
     * @param array $data Response data
     * @param int $statusCode HTTP status code
     * @return void
     */
    private static function sendJsonResponse(array $data, int $statusCode): void
    {
        // Set HTTP status code
        http_response_code($statusCode);
        
        // Set JSON content type
        header('Content-Type: application/json; charset=utf-8');
        
        // Prevent caching for API responses
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        // Output JSON response
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }

    /**
     * Generate a unique request ID for tracking
     * 
     * @return string Unique request identifier
     */
    public static function generateRequestId(): string
    {
        return uniqid('req_', true);
    }
}
