---
type: "always_apply"
---

# Design Document

## Overview

The GuardGeo API Platform is a private backend system built with pure PHP following MVC architecture and SOLID principles. The platform serves as the intelligence hub for the GuardGeo WordPress plugin, providing IP analysis, Freemius integration, and comprehensive admin management. The system is designed for deployment on shared hosting environments with Apache, requiring no complex server configurations or environment-specific setups.

The platform operates as a stateless API service that validates plugin requests through Freemius, enriches visitor data with IP intelligence from ipRegistry, and provides real-time decision-making capabilities for the GuardGeo security plugin.

## Architecture

### High-Level Architecture

The system follows a clean MVC architecture with clear separation of concerns:

```mermaid
---
config:
  layout: elk
---
flowchart LR
    A["public/index.php"] --> B["Router"]
    B -- /admin/* --> C["AdminController"]
    B -- /api/v1/* --> D["ApiController"]
    B -- /webhooks/* --> E["WebhookController"]
    C --> V["Views (src/Views/…)"]
    D --> S["Services"]
    E --> S
    S --> M["Models (DB/Redis)"]
    D -.-> RF["ResponseFormatter"]
    C -.-> RF
    E -.-> RF
```

### Directory Structure

```
guardgeo-api-platform/
├── public/
│   ├── index.php              # Single entry point
│   ├── assets/                # CSS, JS, images
│   └── .htaccess              # Apache rewrite rules
├── src/
│   ├── Controllers/           # MVC Controllers
│   │   ├── AdminController.php
│   │   ├── ApiController.php
│   │   └── WebhookController.php
│   ├── Services/              # Business logic
│   │   ├── FreemiusService.php
│   │   ├── IpRegistryService.php
│   │   ├── AuthService.php
│   │   └── LoggingService.php
│   ├── Models/                # Data access layer
│   │   ├── Product.php
│   │   ├── Installation.php
│   │   ├── IpData.php
│   │   ├── ApiLog.php
│   │   └── User.php
│   ├── Views/                 # HTML templates
│   │   ├── admin/
│   │   └── layouts/
│   ├── Core/                  # Framework components
│   │   ├── Router.php
│   │   ├── Database.php
│   │   ├── Redis.php
│   │   └── ResponseFormatter.php
│   └── Config/                # Configuration
│       └── Database.php
├── database/
│   └── schema.sql             # Single SQL file for setup
├── logs/                      # Application logs
├── docker-compose.yml         # Development container
├── composer.json              # Dependencies
└── README.md                  # Setup instructions
```

### Request Flow

```mermaid
sequenceDiagram
    participant Client as WordPress Plugin
    participant Router as Router
    participant Controller as ApiController
    participant Service as FreemiusService
    participant IpService as IpRegistryService
    participant Model as Models
    participant DB as PostgreSQL
    participant Cache as Redis

    Client->>Router: POST /api/v1/analyze
    Router->>Controller: Route to ApiController
    Controller->>Service: Validate Freemius data
    Service->>Cache: Check cached installation
    alt Cache Hit
        Cache-->>Service: Return cached data
    else Cache Miss
        Service->>Model: Query Freemius API
        Model-->>Service: Installation data
        Service->>Cache: Store in cache
    end
    
    alt Valid Installation
        Controller->>IpService: Get IP intelligence
        IpService->>Model: Check cached IP data
        alt Fresh Data Available
            Model-->>IpService: Return cached IP data
        else Stale/Missing Data
            IpService->>Model: Fetch from ipRegistry
            Model->>DB: Store fresh data
        end
        IpService-->>Controller: IP intelligence data
        Controller->>Model: Log API request
        Controller-->>Client: Success response with IP data
    else Invalid Installation
        Controller->>Model: Log failed request
        Controller-->>Client: 403 Forbidden
    end
```

## Components and Interfaces

### Core Components

#### Router
**Responsibility**: Central request routing and URL parsing
**Interface**:
```php
interface RouterInterface
{
    public function route(string $method, string $uri): array;
    public function addRoute(string $method, string $pattern, string $controller, string $action): void;
}
```

#### Controllers
**AdminController**: Handles web interface for admin users
- Authentication and session management
- Dashboard rendering with statistics
- Log viewing and searching
- Manual IP lookups and system management

**ApiController**: Handles REST API endpoints
- IP analysis endpoint (/api/v1/analyze)
- Request validation and response formatting
- Integration with business services

**WebhookController**: Processes Freemius webhooks
- HMAC signature validation
- Event queuing to Redis
- Webhook response handling

#### Services Layer

**FreemiusService**: Manages Freemius API integration
```php
interface FreemiusServiceInterface
{
    public function validateInstallation(int $pluginId, int $installId, string $url): bool;
    public function getInstallationData(int $installId): ?array;
    public function cacheInstallationData(int $installId, array $data): void;
}
```

**IpRegistryService**: Handles IP intelligence operations
```php
interface IpRegistryServiceInterface
{
    public function getIpData(string $ip): array; // Returns complete ipRegistry response
    public function isDataFresh(string $ip): bool;
    public function fetchFreshData(string $ip): array; // Fetches and stores complete response
}
```

**Note**: The IpRegistryService stores and returns the complete, unmodified ipRegistry API response. This ensures the GuardGeo plugin receives all available IP intelligence data including location, security, carrier, company, connection, currency, timezone, and any future fields added by ipRegistry.

**AuthService**: Manages admin authentication
```php
interface AuthServiceInterface
{
    public function authenticate(string $email, string $password): ?User;
    public function isValidDomain(string $email): bool;
    public function createSession(User $user): string;
}
```

#### Models Layer

**Product**: Freemius product data management
```php
class Product extends BaseModel
{
    public function findById(int $id): ?array;
    public function updateFromWebhook(array $data): bool;
    public function isActive(int $id): bool;
}
```

**Installation**: Freemius installation data management
```php
class Installation extends BaseModel
{
    public function findById(int $id): ?array;
    public function hasActiveLicense(int $id): bool;
    public function updateFromWebhook(array $data): bool;
}
```

**IpData**: IP intelligence data storage
```php
class IpData extends BaseModel
{
    public function findByIp(string $ip): ?array;
    public function store(string $ip, array $completeIpRegistryData): bool;
    public function isDataFresh(string $ip, int $maxAge = 259200): bool; // 3 days
    public function getCompleteData(string $ip): ?array; // Returns full ipRegistry response
}
```

### Database Schema

#### Core Tables

**products**
- id (bigint, primary key)
- freemius_id (bigint, unique)
- secret_key (varchar)
- public_key (varchar)
- parent_plugin_id (bigint, nullable)
- developer_id (bigint)
- store_id (bigint)
- slug (varchar)
- title (varchar)
- environment (integer) -- 0=production, 1=sandbox
- icon (varchar, nullable)
- default_plan_id (bigint)
- plans (text) -- comma separated plan IDs
- features (text) -- comma separated feature IDs
- money_back_period (integer)
- refund_policy (varchar) -- flexible, moderate, strict
- annual_renewals_discount (integer, nullable)
- renewals_discount_type (varchar) -- percentage, dollar
- is_released (boolean)
- is_sdk_required (boolean)
- is_pricing_visible (boolean)
- is_wp_org_compliant (boolean)
- installs_count (integer)
- active_installs_count (integer)
- free_releases_count (integer)
- premium_releases_count (integer)
- total_purchases (integer)
- total_subscriptions (integer)
- total_renewals (integer)
- total_failed_purchases (integer)
- earnings (decimal(10,2))
- type (varchar) -- plugin, theme, widget, template
- is_static (boolean)
- created_at (timestamp)
- updated_at (timestamp)

**installations**
- id (bigint, primary key)
- freemius_id (bigint, unique)
- secret_key (varchar)
- public_key (varchar)
- site_id (bigint)
- plugin_id (bigint, foreign key to products.freemius_id)
- user_id (bigint)
- url (varchar, nullable)
- title (varchar, nullable)
- version (varchar)
- plan_id (bigint, nullable)
- license_id (bigint, nullable)
- trial_plan_id (bigint, nullable)
- trial_ends (timestamp, nullable)
- subscription_id (bigint, nullable)
- gross (decimal(10,2))
- country_code (varchar(2), nullable)
- language (varchar, nullable)
- platform_version (varchar, nullable)
- sdk_version (varchar, nullable)
- programming_language_version (varchar, nullable)
- is_active (boolean)
- is_disconnected (boolean)
- is_premium (boolean)
- is_uninstalled (boolean)
- is_locked (boolean)
- source (integer) -- migration source
- upgraded (timestamp, nullable)
- last_seen_at (timestamp, nullable)
- last_served_update_version (varchar, nullable)
- created_at (timestamp)
- updated_at (timestamp)

**ip_data**
- id (bigint, primary key)
- ip_address (inet, unique)
- raw_data (jsonb) -- Complete ipRegistry response stored as-is
- created_at (timestamp)
- updated_at (timestamp)
- INDEX on ip_address for fast lookups
- INDEX on created_at for data freshness queries

**api_logs**
- id (bigint, primary key)
- ip_address (inet)
- method (varchar)
- endpoint (varchar)
- request_data (jsonb)
- response_status (integer)
- response_data (jsonb)
- processing_time (integer) -- milliseconds
- created_at (timestamp)

**admin_users**
- id (bigint, primary key)
- email (varchar, unique)
- password_hash (varchar)
- role (enum: super_admin, dev, marketing, sales)
- is_active (boolean)
- last_login (timestamp)
- created_at (timestamp)
- updated_at (timestamp)

### Redis Cache Structure

**Installation Cache**
- Key: `installation:{install_id}`
- TTL: 3600 seconds (1 hour)
- Data: Complete installation object from Freemius

**Product Cache**
- Key: `product:{product_id}`
- TTL: 3600 seconds (1 hour)
- Data: Product information and status

**Webhook Queue**
- Key: `webhook_queue`
- Type: List
- Data: Webhook events for background processing

## Data Models

### API Request/Response Models

**IP Analysis Request**
```json
{
  "ip": "***********",
  "visitor_hash": "uuid-v4-string",
  "plugin_id": 12345,
  "install_id": 67890,
  "url": "https://example.com"
}
```

**IP Analysis Response (Success)**
```json
{
  "success": true,
  "data": {
    "ip": "**********",
    "type": "IPv4",
    "hostname": "host.example.com",
    "carrier": {
      "name": "Verizon Wireless",
      "mcc": "310",
      "mnc": "004"
    },
    "company": {
      "domain": "verizon.com",
      "name": "Verizon Communications Inc.",
      "type": "isp"
    },
    "connection": {
      "asn": 22394,
      "domain": "verizon.com",
      "organization": "Verizon Wireless",
      "route": "**********/16",
      "type": "cellular"
    },
    "currency": {
      "code": "USD",
      "name": "US Dollar",
      "name_native": "US Dollar",
      "plural": "US dollars",
      "plural_native": "US dollars",
      "symbol": "$",
      "symbol_native": "$",
      "format": {
        "negative": {
          "prefix": "-$",
          "suffix": ""
        },
        "positive": {
          "prefix": "$",
          "suffix": ""
        }
      }
    },
    "location": {
      "continent": {
        "code": "NA",
        "name": "North America"
      },
      "country": {
        "area": 9629091,
        "borders": ["CAN", "MEX"],
        "calling_code": "1",
        "capital": "Washington D.C.",
        "code": "US",
        "name": "United States",
        "population": 327167434,
        "population_density": 33.6,
        "flag": {
          "emoji": "🇺🇸",
          "emoji_unicode": "U+1F1FA U+1F1F8",
          "emojitwo": "https://cdn.ipregistry.co/flags/emojitwo/us.svg",
          "noto": "https://cdn.ipregistry.co/flags/noto/us.png",
          "twemoji": "https://cdn.ipregistry.co/flags/twemoji/us.svg",
          "wikimedia": "https://cdn.ipregistry.co/flags/wikimedia/us.svg"
        },
        "languages": [
          {
            "code": "en",
            "name": "English",
            "native": "English"
          }
        ],
        "tld": ".us"
      },
      "region": {
        "code": "CA",
        "name": "California"
      },
      "city": "Mountain View",
      "postal": "94043",
      "latitude": 37.419200897216797,
      "longitude": -122.05740356445312,
      "language": {
        "code": "en",
        "name": "English",
        "native": "English"
      },
      "in_eu": false
    },
    "security": {
      "is_abuser": false,
      "is_attacker": false,
      "is_bogon": false,
      "is_cloud_provider": false,
      "is_proxy": false,
      "is_relay": false,
      "is_tor": false,
      "is_tor_exit": false,
      "is_vpn": false,
      "is_anonymous": false,
      "is_threat": false
    },
    "time_zone": {
      "id": "America/Los_Angeles",
      "abbreviation": "PST",
      "current_time": "2025-01-20T02:30:00-08:00",
      "name": "Pacific Standard Time",
      "offset": -28800,
      "in_daylight_saving": false
    },
    "_meta": {
      "cached_at": "2025-01-20T10:30:00Z",
      "fresh_until": "2025-01-23T10:30:00Z"
    }
  }
}
```

**Error Response**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_INSTALLATION",
    "message": "Installation not found or inactive",
    "details": "Install ID 67890 does not exist or has been deactivated"
  }
}
```

### Freemius Integration Models

Based on the Freemius API specification, the system will handle:

**Installation Object**
- Maps to Freemius Install entity
- Includes validation fields: is_active, is_uninstalled, license_id
- Cached for performance with webhook-based invalidation

**Product Object**
- Maps to Freemius Plugin/Product entity
- Tracks active status and basic metadata
- Used for installation validation

## Error Handling

### Error Categories

**Validation Errors (400)**
- Missing required parameters
- Invalid IP address format
- Invalid plugin/installation IDs

**Authentication Errors (401/403)**
- Invalid Freemius installation
- Inactive or uninstalled plugin
- No active license

**Service Errors (500)**
- Database connection failures
- External API failures (Freemius, ipRegistry)
- Redis connection issues

**Rate Limiting (429)**
- Too many requests from same IP
- API quota exceeded

### Error Response Format

All errors follow consistent JSON structure:
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": "Additional context when available"
  },
  "request_id": "unique-request-identifier"
}
```

### Logging Strategy

**Structured Logging**: All logs use JSON format with consistent fields
- timestamp, level, message, context, request_id
- Separate log files: api.log, webhook.log, admin.log, error.log

**Log Levels**:
- ERROR: System failures, external API errors
- WARN: Validation failures, suspicious activity
- INFO: Successful requests, admin actions
- DEBUG: Detailed request/response data (when debug enabled)

## Testing Strategy

### Manual Testing Approach

Since the requirements specify no automated tests, the testing strategy focuses on comprehensive manual verification:

**API Testing**
- Postman/Insomnia collections for all endpoints
- Test cases for valid/invalid Freemius installations
- IP analysis with various IP types (residential, VPN, Tor, etc.)
- Error condition testing (invalid parameters, service failures)

**Integration Testing**
- Freemius webhook processing
- ipRegistry API integration
- Database operations and caching
- Redis queue processing

**Admin Interface Testing**
- Authentication flows for all user roles
- Dashboard functionality and statistics
- Log viewing and searching
- Manual IP lookup features

**Deployment Testing**
- Shared hosting compatibility
- Apache .htaccess configuration
- Database setup with single SQL file
- Docker development environment

### Test Data Management

**Development Database**
- Sample Freemius products and installations
- Test IP addresses with known characteristics
- Admin users for each role type
- Historical API logs for dashboard testing

**Mock Services**
- Freemius API responses for various scenarios
- ipRegistry responses for different IP types
- Webhook payloads for testing event processing

### Performance Testing

**Load Testing**
- API endpoint performance under concurrent requests
- Database query optimization verification
- Redis caching effectiveness
- Memory usage monitoring

**Monitoring**
- Response time tracking
- Error rate monitoring
- Cache hit/miss ratios
- Database connection pooling efficiency