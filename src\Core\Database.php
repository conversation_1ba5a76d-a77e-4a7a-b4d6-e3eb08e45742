<?php

namespace Skpassegna\GuardgeoApi\Core;

use PDO;
use PDOException;
use Skpassegna\GuardgeoApi\Config\Database as DatabaseConfig;

/**
 * Database Class
 * 
 * Manages PostgreSQL database connections with connection pooling and error handling.
 * Implements singleton pattern for connection management and provides query execution methods.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class Database
{
    /**
     * @var PDO|null Database connection instance
     */
    private static ?PDO $connection = null;

    /**
     * @var array Database configuration
     */
    private static array $config = [];

    /**
     * @var bool Connection status
     */
    private static bool $connected = false;

    /**
     * Initialize database configuration
     * 
     * @return void
     */
    public static function initialize(): void
    {
        self::$config = DatabaseConfig::getConfig();
    }

    /**
     * Get database connection
     * 
     * @return PDO Database connection
     * @throws PDOException If connection fails
     */
    public static function getConnection(): PDO
    {
        if (self::$connection === null || !self::$connected) {
            self::connect();
        }
        
        return self::$connection;
    }

    /**
     * Establish database connection
     * 
     * @return void
     * @throws PDOException If connection fails
     */
    private static function connect(): void
    {
        if (empty(self::$config)) {
            self::initialize();
        }

        $dsn = sprintf(
            'pgsql:host=%s;port=%s;dbname=%s',
            self::$config['host'],
            self::$config['port'],
            self::$config['database']
        );

        try {
            self::$connection = new PDO(
                $dsn,
                self::$config['username'],
                self::$config['password'],
                self::$config['options']
            );
            
            self::$connected = true;
            
            // Set charset
            self::$connection->exec("SET NAMES 'UTF8'");
            
        } catch (PDOException $e) {
            self::$connected = false;
            error_log('Database connection failed: ' . $e->getMessage());
            throw new PDOException('Database connection failed: ' . $e->getMessage());
        }
    }

    /**
     * Execute a query and return results
     * 
     * @param string $query SQL query
     * @param array $params Query parameters
     * @return array Query results
     * @throws PDOException If query fails
     */
    public static function query(string $query, array $params = []): array
    {
        try {
            $connection = self::getConnection();
            $statement = $connection->prepare($query);
            $statement->execute($params);
            
            return $statement->fetchAll();
            
        } catch (PDOException $e) {
            error_log('Database query failed: ' . $e->getMessage());
            throw new PDOException('Database query failed: ' . $e->getMessage());
        }
    }

    /**
     * Execute a query and return a single row
     * 
     * @param string $query SQL query
     * @param array $params Query parameters
     * @return array|null Single row or null if not found
     * @throws PDOException If query fails
     */
    public static function queryOne(string $query, array $params = []): ?array
    {
        try {
            $connection = self::getConnection();
            $statement = $connection->prepare($query);
            $statement->execute($params);
            
            $result = $statement->fetch();
            return $result ?: null;
            
        } catch (PDOException $e) {
            error_log('Database query failed: ' . $e->getMessage());
            throw new PDOException('Database query failed: ' . $e->getMessage());
        }
    }

    /**
     * Execute an insert/update/delete query
     * 
     * @param string $query SQL query
     * @param array $params Query parameters
     * @return int Number of affected rows
     * @throws PDOException If query fails
     */
    public static function execute(string $query, array $params = []): int
    {
        try {
            $connection = self::getConnection();
            $statement = $connection->prepare($query);
            $statement->execute($params);
            
            return $statement->rowCount();
            
        } catch (PDOException $e) {
            error_log('Database execute failed: ' . $e->getMessage());
            throw new PDOException('Database execute failed: ' . $e->getMessage());
        }
    }

    /**
     * Get the last inserted ID
     * 
     * @param string|null $sequence Sequence name for PostgreSQL
     * @return string Last insert ID
     */
    public static function lastInsertId(?string $sequence = null): string
    {
        return self::getConnection()->lastInsertId($sequence);
    }

    /**
     * Begin a database transaction
     * 
     * @return bool True on success
     */
    public static function beginTransaction(): bool
    {
        return self::getConnection()->beginTransaction();
    }

    /**
     * Commit a database transaction
     * 
     * @return bool True on success
     */
    public static function commit(): bool
    {
        return self::getConnection()->commit();
    }

    /**
     * Rollback a database transaction
     * 
     * @return bool True on success
     */
    public static function rollback(): bool
    {
        return self::getConnection()->rollBack();
    }

    /**
     * Check if database is connected
     * 
     * @return bool Connection status
     */
    public static function isConnected(): bool
    {
        return self::$connected && self::$connection !== null;
    }

    /**
     * Test database connection
     * 
     * @return bool True if connection is working
     */
    public static function testConnection(): bool
    {
        try {
            $connection = self::getConnection();
            $statement = $connection->query('SELECT 1');
            return $statement !== false;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * Close database connection
     * 
     * @return void
     */
    public static function close(): void
    {
        self::$connection = null;
        self::$connected = false;
    }

    /**
     * Get database configuration (without sensitive data)
     * 
     * @return array Safe configuration data
     */
    public static function getConfig(): array
    {
        return [
            'host' => self::$config['host'] ?? 'unknown',
            'port' => self::$config['port'] ?? 'unknown',
            'database' => self::$config['database'] ?? 'unknown',
            'charset' => self::$config['charset'] ?? 'utf8',
        ];
    }
}
