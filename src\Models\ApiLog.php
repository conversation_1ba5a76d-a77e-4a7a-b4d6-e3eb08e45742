<?php

namespace Skpassegna\GuardgeoApi\Models;

use Skpassegna\GuardgeoApi\Core\Database;
use PDOException;

/**
 * ApiLog Model
 * 
 * Manages API request and response logging for monitoring, analytics, and debugging.
 * Stores complete request/response data with performance metrics.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class ApiLog extends BaseModel
{
    /**
     * @var string Table name
     */
    protected string $table = 'api_logs';

    /**
     * @var array Fillable columns for mass assignment
     */
    protected array $fillable = [
        'ip_address',
        'method',
        'endpoint',
        'request_data',
        'response_status',
        'response_data',
        'processing_time'
    ];

    /**
     * @var array Columns that should be cast to specific types
     */
    protected array $casts = [
        'request_data' => 'json',
        'response_data' => 'json',
        'response_status' => 'int',
        'processing_time' => 'int'
    ];

    /**
     * @var bool Disable automatic timestamps (we only use created_at)
     */
    protected bool $timestamps = false;

    /**
     * Log an API request
     * 
     * @param string $method HTTP method
     * @param string $endpoint API endpoint
     * @param string $ipAddress Client IP address
     * @param array|null $requestData Request data
     * @param int $responseStatus HTTP response status
     * @param array|null $responseData Response data
     * @param int $processingTime Processing time in milliseconds
     * @return bool True on success
     */
    public function logRequest(
        string $method,
        string $endpoint,
        string $ipAddress,
        ?array $requestData = null,
        int $responseStatus = 200,
        ?array $responseData = null,
        int $processingTime = 0
    ): bool {
        $data = [
            'ip_address' => $ipAddress,
            'method' => strtoupper($method),
            'endpoint' => $endpoint,
            'request_data' => $requestData ? json_encode($requestData) : null,
            'response_status' => $responseStatus,
            'response_data' => $responseData ? json_encode($responseData) : null,
            'processing_time' => $processingTime,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->create($data) !== null;
    }

    /**
     * Get API logs with filtering and pagination
     * 
     * @param array $filters Filter criteria
     * @param array $options Query options (limit, offset, order)
     * @return array Array of API logs
     */
    public function getLogs(array $filters = [], array $options = []): array
    {
        try {
            $whereConditions = [];
            $params = [];
            
            // Build filter conditions
            if (isset($filters['ip_address'])) {
                $whereConditions[] = "ip_address = :ip_address";
                $params['ip_address'] = $filters['ip_address'];
            }
            
            if (isset($filters['method'])) {
                $whereConditions[] = "method = :method";
                $params['method'] = strtoupper($filters['method']);
            }
            
            if (isset($filters['endpoint'])) {
                $whereConditions[] = "endpoint LIKE :endpoint";
                $params['endpoint'] = '%' . $filters['endpoint'] . '%';
            }
            
            if (isset($filters['status_code'])) {
                $whereConditions[] = "response_status = :status_code";
                $params['status_code'] = (int)$filters['status_code'];
            }
            
            if (isset($filters['status_range'])) {
                if ($filters['status_range'] === 'success') {
                    $whereConditions[] = "response_status >= 200 AND response_status < 300";
                } elseif ($filters['status_range'] === 'error') {
                    $whereConditions[] = "response_status >= 400";
                } elseif ($filters['status_range'] === 'client_error') {
                    $whereConditions[] = "response_status >= 400 AND response_status < 500";
                } elseif ($filters['status_range'] === 'server_error') {
                    $whereConditions[] = "response_status >= 500";
                }
            }
            
            if (isset($filters['date_from'])) {
                $whereConditions[] = "created_at >= :date_from";
                $params['date_from'] = $filters['date_from'];
            }
            
            if (isset($filters['date_to'])) {
                $whereConditions[] = "created_at <= :date_to";
                $params['date_to'] = $filters['date_to'];
            }
            
            if (isset($filters['min_processing_time'])) {
                $whereConditions[] = "processing_time >= :min_processing_time";
                $params['min_processing_time'] = (int)$filters['min_processing_time'];
            }
            
            $query = "SELECT * FROM {$this->table}";
            
            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(' AND ', $whereConditions);
            }
            
            // Add ordering
            $order = $options['order'] ?? 'created_at DESC';
            $query .= " ORDER BY " . $order;
            
            // Add limit and offset
            if (isset($options['limit'])) {
                $query .= " LIMIT " . (int)$options['limit'];
                
                if (isset($options['offset'])) {
                    $query .= " OFFSET " . (int)$options['offset'];
                }
            }
            
            $results = Database::query($query, $params);
            
            return array_map([$this, 'castAttributes'], $results);
            
        } catch (PDOException $e) {
            error_log("Error getting API logs: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get API statistics
     * 
     * @param array $filters Filter criteria
     * @return array API statistics
     */
    public function getStatistics(array $filters = []): array
    {
        try {
            $whereConditions = [];
            $params = [];
            
            // Build filter conditions (same as getLogs)
            if (isset($filters['date_from'])) {
                $whereConditions[] = "created_at >= :date_from";
                $params['date_from'] = $filters['date_from'];
            }
            
            if (isset($filters['date_to'])) {
                $whereConditions[] = "created_at <= :date_to";
                $params['date_to'] = $filters['date_to'];
            }
            
            $whereClause = !empty($whereConditions) ? " WHERE " . implode(' AND ', $whereConditions) : "";
            
            // Total requests
            $totalQuery = "SELECT COUNT(*) as count FROM {$this->table}" . $whereClause;
            $totalResult = Database::queryOne($totalQuery, $params);
            $totalRequests = (int)($totalResult['count'] ?? 0);
            
            // Success requests (2xx)
            $successQuery = "SELECT COUNT(*) as count FROM {$this->table}" . $whereClause . 
                           ($whereClause ? " AND" : " WHERE") . " response_status >= 200 AND response_status < 300";
            $successResult = Database::queryOne($successQuery, $params);
            $successRequests = (int)($successResult['count'] ?? 0);
            
            // Error requests (4xx + 5xx)
            $errorQuery = "SELECT COUNT(*) as count FROM {$this->table}" . $whereClause . 
                         ($whereClause ? " AND" : " WHERE") . " response_status >= 400";
            $errorResult = Database::queryOne($errorQuery, $params);
            $errorRequests = (int)($errorResult['count'] ?? 0);
            
            // Average processing time
            $avgTimeQuery = "SELECT AVG(processing_time) as avg_time FROM {$this->table}" . $whereClause;
            $avgTimeResult = Database::queryOne($avgTimeQuery, $params);
            $avgProcessingTime = (float)($avgTimeResult['avg_time'] ?? 0);
            
            // Top endpoints
            $topEndpointsQuery = "SELECT endpoint, COUNT(*) as count FROM {$this->table}" . $whereClause . 
                               " GROUP BY endpoint ORDER BY count DESC LIMIT 10";
            $topEndpoints = Database::query($topEndpointsQuery, $params);
            
            // Top IP addresses
            $topIpsQuery = "SELECT ip_address, COUNT(*) as count FROM {$this->table}" . $whereClause . 
                          " GROUP BY ip_address ORDER BY count DESC LIMIT 10";
            $topIps = Database::query($topIpsQuery, $params);
            
            return [
                'total_requests' => $totalRequests,
                'success_requests' => $successRequests,
                'error_requests' => $errorRequests,
                'success_rate' => $totalRequests > 0 ? round(($successRequests / $totalRequests) * 100, 2) : 0,
                'error_rate' => $totalRequests > 0 ? round(($errorRequests / $totalRequests) * 100, 2) : 0,
                'avg_processing_time' => round($avgProcessingTime, 2),
                'top_endpoints' => $topEndpoints,
                'top_ips' => $topIps
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting API statistics: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get hourly request statistics for the last 24 hours
     * 
     * @return array Hourly statistics
     */
    public function getHourlyStatistics(): array
    {
        try {
            $query = "SELECT 
                        DATE_TRUNC('hour', created_at) as hour,
                        COUNT(*) as total_requests,
                        COUNT(CASE WHEN response_status >= 200 AND response_status < 300 THEN 1 END) as success_requests,
                        COUNT(CASE WHEN response_status >= 400 THEN 1 END) as error_requests,
                        AVG(processing_time) as avg_processing_time
                      FROM {$this->table}
                      WHERE created_at >= NOW() - INTERVAL '24 hours'
                      GROUP BY hour
                      ORDER BY hour DESC";
            
            return Database::query($query);
            
        } catch (PDOException $e) {
            error_log("Error getting hourly statistics: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get slow requests (above threshold)
     * 
     * @param int $threshold Processing time threshold in milliseconds
     * @param int $limit Number of results to return
     * @return array Slow requests
     */
    public function getSlowRequests(int $threshold = 5000, int $limit = 50): array
    {
        try {
            $query = "SELECT * FROM {$this->table}
                      WHERE processing_time >= :threshold
                      ORDER BY processing_time DESC
                      LIMIT :limit";
            
            return Database::query($query, [
                'threshold' => $threshold,
                'limit' => $limit
            ]);
            
        } catch (PDOException $e) {
            error_log("Error getting slow requests: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Clean up old API logs
     * 
     * @param int $daysToKeep Number of days to keep logs
     * @return int Number of records deleted
     */
    public function cleanupOldLogs(int $daysToKeep = 30): int
    {
        try {
            $query = "DELETE FROM {$this->table} 
                      WHERE created_at < NOW() - INTERVAL ':days days'";
            
            return Database::execute($query, ['days' => $daysToKeep]);
            
        } catch (PDOException $e) {
            error_log("Error cleaning up old logs: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get request count by IP address
     * 
     * @param string $ipAddress IP address
     * @param string $timeframe Timeframe (hour, day, week, month)
     * @return int Request count
     */
    public function getRequestCountByIp(string $ipAddress, string $timeframe = 'hour'): int
    {
        try {
            $intervals = [
                'hour' => '1 hour',
                'day' => '1 day',
                'week' => '1 week',
                'month' => '1 month'
            ];
            
            $interval = $intervals[$timeframe] ?? '1 hour';
            
            $query = "SELECT COUNT(*) as count FROM {$this->table}
                      WHERE ip_address = :ip_address 
                      AND created_at >= NOW() - INTERVAL ':interval'";
            
            $result = Database::queryOne($query, [
                'ip_address' => $ipAddress,
                'interval' => $interval
            ]);
            
            return (int)($result['count'] ?? 0);
            
        } catch (PDOException $e) {
            error_log("Error getting request count by IP: " . $e->getMessage());
            return 0;
        }
    }
}
