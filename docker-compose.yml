services:
  # PHP Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: guardgeo_api_app
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - ./logs:/var/www/html/logs
    environment:
      - DEBUG=true
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=guardgeo_api
      - DB_USER=guardgeo_user
      - DB_PASS=guardgeo_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - TIMEZONE=UTC
    depends_on:
      - postgres
      - redis
    networks:
      - guardgeo_network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: guardgeo_api_postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: guardgeo_api
      POSTGRES_USER: guardgeo_user
      POSTGRES_PASSWORD: guardgeo_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    networks:
      - guardgeo_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: guardgeo_api_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - guardgeo_network

  # pgAdmin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: guardgeo_api_pgadmin
    ports:
      - "8081:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - guardgeo_network

  # Redis Commander (Optional - for Redis management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: guardgeo_api_redis_commander
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
    depends_on:
      - redis
    networks:
      - guardgeo_network

volumes:
  postgres_data:
  redis_data:
  pgadmin_data:

networks:
  guardgeo_network:
    driver: bridge
