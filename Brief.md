By analyzing deeply the `docs/`, `docs/specifications.md`, and `ALL-FREEMIUS-ENTITIES.md` for the GuargGeo API, identify the intentions and the best system design + design system + architecture of this project. The platform should be a MVC + strict SOLID principles; no different env config (dev, stagging, prod) just the option to enable or not the debugging. No configs and scripts files, no migrations scripts, etc. ; just a website entire coded to be uploaded on a FTP and works completely with a complete and single complete sql file to create tables using phpmyadmin, no PHP framework, no absurd/illogical/unfinished/mock code/methods/classes/architecture/reasoning code and or implementations.
Add entirely a direct and simple docker compose file too.

* Expected architecture:

```mermaid
---
config:
  layout: elk
---
flowchart LR
    A["public/index.php"] --> B["Router"]
    B -- /admin/* --> C["AdminController"]
    B -- /api/v1/* --> D["ApiController"]
    B -- /webhooks/* --> E["WebhookController"]
    C --> V["Views (src/Views/…)"]
    D --> S["Services"]
    E --> S
    S --> M["Models (DB/Redis)"]
    D -.-> RF["ResponseFormatter"]
    C -.-> RF
    E -.-> RF
```

-----

Ground your analysis ONLY on the documents & integrated libraries.

- No backward compatibility. What does not fit the new architecture and design must be entirely and purely removed and deleted
- The project will be deployed on a shared hosting that use Apache
