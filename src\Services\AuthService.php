<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Models\User;
use Skpassegna\GuardgeoApi\Config\Database;
use Skpassegna\GuardgeoApi\Core\SessionManager;

/**
 * Authentication Service Class
 * 
 * Handles user authentication, session management, and email domain validation
 * for the GuardGeo API Platform admin interface.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class AuthService
{
    /**
     * @var User User model instance
     */
    private User $userModel;

    /**
     * @var array Application configuration
     */
    private array $config;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->userModel = new User();
        $this->config = Database::getAppConfig();

        // Initialize secure session management
        SessionManager::initialize();
    }

    /**
     * Authenticate user with email and password
     * 
     * @param string $email User email
     * @param string $password User password
     * @return array|null User data on success, null on failure
     */
    public function authenticate(string $email, string $password): ?array
    {
        try {
            // Validate email format
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return null;
            }

            // Validate email domain
            if (!$this->isValidDomain($email)) {
                error_log("Authentication failed: Invalid email domain for $email");
                return null;
            }

            // Find active user by email
            $user = $this->userModel->findActiveByEmail($email);
            if (!$user) {
                error_log("Authentication failed: User not found or inactive for $email");
                return null;
            }

            // Verify password
            if (!$this->userModel->verifyPassword($password, $user['password_hash'])) {
                error_log("Authentication failed: Invalid password for $email");
                return null;
            }

            // Update last login timestamp
            $this->userModel->updateLastLogin($user['id']);

            // Remove sensitive data from user array
            unset($user['password_hash']);

            return $user;

        } catch (\Exception $e) {
            error_log("Authentication error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if email domain is allowed
     * 
     * @param string $email Email address to validate
     * @return bool True if domain is allowed
     */
    public function isValidDomain(string $email): bool
    {
        $allowedDomains = $this->config['admin']['allowed_domains'] ?? [];
        
        if (empty($allowedDomains)) {
            return false;
        }

        $emailDomain = strtolower(substr(strrchr($email, "@"), 1));
        
        foreach ($allowedDomains as $allowedDomain) {
            if (strtolower(trim($allowedDomain)) === $emailDomain) {
                return true;
            }
        }

        return false;
    }

    /**
     * Create user session
     *
     * @param array $user User data
     * @return string Session ID
     */
    public function createSession(array $user): string
    {
        // Regenerate session ID for security
        SessionManager::regenerateId();

        // Store user data in session
        SessionManager::set('user_id', $user['id']);
        SessionManager::set('user_email', $user['email']);
        SessionManager::set('user_role', $user['role']);
        SessionManager::set('login_time', time());
        SessionManager::set('last_activity', time());

        return SessionManager::getId();
    }

    /**
     * Destroy user session
     *
     * @return bool True on success
     */
    public function destroySession(): bool
    {
        return SessionManager::destroy();
    }

    /**
     * Check if user is authenticated
     *
     * @return bool True if user is authenticated
     */
    public function isAuthenticated(): bool
    {
        return SessionManager::has('user_id') &&
               SessionManager::has('login_time') &&
               $this->isSessionValid();
    }

    /**
     * Check if current session is valid
     *
     * @return bool True if session is valid
     */
    public function isSessionValid(): bool
    {
        $lastActivity = SessionManager::get('last_activity');
        if (!$lastActivity) {
            return false;
        }

        $sessionTimeout = $this->config['admin']['session_timeout'] ?? 3600;
        $timeSinceLastActivity = time() - $lastActivity;

        if ($timeSinceLastActivity > $sessionTimeout) {
            $this->destroySession();
            return false;
        }

        // Update last activity timestamp
        SessionManager::set('last_activity', time());

        return true;
    }

    /**
     * Get current authenticated user
     *
     * @return array|null Current user data or null if not authenticated
     */
    public function getCurrentUser(): ?array
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        $userId = SessionManager::get('user_id');
        $user = $this->userModel->findById($userId);

        if (!$user || !$user['is_active']) {
            $this->destroySession();
            return null;
        }

        // Remove sensitive data
        unset($user['password_hash']);

        return $user;
    }

    /**
     * Check if current user has specific permission
     * 
     * @param string $permission Permission to check
     * @return bool True if user has permission
     */
    public function hasPermission(string $permission): bool
    {
        $user = $this->getCurrentUser();
        
        if (!$user) {
            return false;
        }

        return $this->userModel->hasPermission($user, $permission);
    }

    /**
     * Check if current user can manage other users
     * 
     * @return bool True if user can manage users
     */
    public function canManageUsers(): bool
    {
        return $this->hasPermission('user_management');
    }

    /**
     * Check if current user can access technical tools
     * 
     * @return bool True if user can access technical tools
     */
    public function canAccessTechnicalTools(): bool
    {
        return $this->hasPermission('technical_tools');
    }

    /**
     * Require authentication for protected routes
     * 
     * @return array Current user data
     * @throws \Exception If user is not authenticated
     */
    public function requireAuth(): array
    {
        $user = $this->getCurrentUser();
        
        if (!$user) {
            throw new \Exception('Authentication required', 401);
        }

        return $user;
    }

    /**
     * Require specific permission for protected actions
     * 
     * @param string $permission Required permission
     * @return array Current user data
     * @throws \Exception If user doesn't have permission
     */
    public function requirePermission(string $permission): array
    {
        $user = $this->requireAuth();
        
        if (!$this->userModel->hasPermission($user, $permission)) {
            throw new \Exception('Insufficient permissions', 403);
        }

        return $user;
    }

    /**
     * Validate password strength
     * 
     * @param string $password Password to validate
     * @return array Validation result with success status and errors
     */
    public function validatePassword(string $password): array
    {
        $errors = [];
        $minLength = $this->config['admin']['password_min_length'] ?? 12;

        if (strlen($password) < $minLength) {
            $errors[] = "Password must be at least {$minLength} characters long";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Get allowed email domains
     * 
     * @return array Array of allowed email domains
     */
    public function getAllowedDomains(): array
    {
        return $this->config['admin']['allowed_domains'] ?? [];
    }

    /**
     * Get session timeout in seconds
     * 
     * @return int Session timeout in seconds
     */
    public function getSessionTimeout(): int
    {
        return $this->config['admin']['session_timeout'] ?? 3600;
    }

    /**
     * Get time remaining in current session
     *
     * @return int Time remaining in seconds, 0 if not authenticated
     */
    public function getSessionTimeRemaining(): int
    {
        if (!$this->isAuthenticated()) {
            return 0;
        }

        $sessionTimeout = $this->getSessionTimeout();
        $lastActivity = SessionManager::get('last_activity', 0);
        $timeSinceLastActivity = time() - $lastActivity;

        return max(0, $sessionTimeout - $timeSinceLastActivity);
    }

    /**
     * Extend current session
     *
     * @return bool True on success
     */
    public function extendSession(): bool
    {
        if (!$this->isAuthenticated()) {
            return false;
        }

        SessionManager::set('last_activity', time());
        return true;
    }
}
