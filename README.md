# GuardGeo API Platform

A private backend system for the GuardGeo WordPress plugin, providing IP intelligence and fraud prevention capabilities.

## Overview

The GuardGeo API Platform is a proprietary backend system designed to serve the GuardGeo WordPress plugin with IP intelligence and fraud prevention capabilities. The platform validates plugin requests via Freemius integration, fetches IP intelligence data from ipRegistry, and provides comprehensive admin management tools.

**Core Architecture**: Pure PHP with MVC architecture following SOLID principles, deployable on shared hosting via FTP with a single SQL file for database setup.

## Features

### Core Functionality
- **IP Intelligence**: Complete ipRegistry integration for comprehensive IP analysis
- **Freemius Integration**: Plugin validation, license management, and webhook processing
- **Admin Interface**: Modern web-based administration panel with role-based access
- **RESTful API**: Clean API endpoints for WordPress plugin communication
- **Real-time Processing**: Webhook handling with Redis queue system
- **Comprehensive Logging**: Detailed request tracking and system monitoring

### Security Features
- Domain-restricted admin authentication
- CSRF protection and XSS prevention
- SQL injection protection
- Rate limiting and DDoS protection
- Secure session management
- Input validation and sanitization

### Performance Features
- Redis caching with fallback to file-based caching
- Database query optimization
- Compressed responses
- Static asset caching
- Connection pooling

## Requirements

### Server Requirements
- **PHP**: 8.0 or higher with extensions:
  - PDO with PostgreSQL support
  - Redis extension (optional)
  - cURL extension
  - JSON extension
  - OpenSSL extension
- **Database**: PostgreSQL 12 or higher
- **Cache**: Redis 6+ (optional, falls back to file-based caching)
- **Web Server**: Apache 2.4+ with mod_rewrite enabled
- **Memory**: Minimum 256MB PHP memory limit
- **Storage**: Minimum 100MB disk space

### Development Requirements
- Composer for dependency management
- Docker and Docker Compose (for development environment)
- Git for version control

## Installation

### Shared Hosting Deployment

1. **Download and Extract**
   ```bash
   # Download the latest release
   wget https://github.com/your-org/guardgeo-api/releases/latest/download/guardgeo-api.zip
   unzip guardgeo-api.zip
   ```

2. **Upload Files**
   - Upload all files to your hosting account
   - Ensure the `public/` directory is your web root
   - Set proper file permissions (644 for files, 755 for directories)

3. **Database Setup**
   ```bash
   # Import the database schema
   psql -h your_host -U your_user -d your_database < database/schema.sql
   ```

4. **Configuration**
   - Copy `src/Config/Database.php.example` to `src/Config/Database.php`
   - Edit database credentials and API keys
   - Ensure `logs/` directory is writable

5. **Create Super Admin**
   ```sql
   INSERT INTO admin_users (email, password_hash, role, is_active, created_at)
   VALUES ('<EMAIL>', '$2y$10$hash_here', 'super_admin', true, NOW());
   ```

### Local Development Setup

1. **Clone Repository**
   ```bash
   git clone https://github.com/your-org/guardgeo-api.git
   cd guardgeo-api
   ```

2. **Install Dependencies**
   ```bash
   composer install
   ```

3. **Start Development Environment**
   ```bash
   docker-compose up -d
   ```

4. **Initialize Database**
   ```bash
   docker-compose exec db psql -U guardgeo -d guardgeo_api < database/schema.sql
   ```

5. **Access Application**
   - API: http://localhost:8080
   - Admin: http://localhost:8080/admin
   - Database: http://localhost:8081 (Adminer)

## Configuration

### Database Configuration

Edit `src/Config/Database.php`:

```php
<?php
return [
    'host' => 'your_host',
    'port' => 5432,
    'database' => 'guardgeo_api',
    'username' => 'your_username',
    'password' => 'your_password',
    'redis' => [
        'host' => 'localhost',
        'port' => 6379,
        'password' => null,
        'database' => 0
    ]
];
```

### API Keys Configuration

Add your API credentials to the configuration:

```php
// ipRegistry API Key
define('IPREGISTRY_API_KEY', 'your_ipregistry_api_key');

// Freemius API Credentials
define('FREEMIUS_API_URL', 'https://api.freemius.com');
define('FREEMIUS_PUBLIC_KEY', 'your_freemius_public_key');
define('FREEMIUS_SECRET_KEY', 'your_freemius_secret_key');
```

### Admin User Roles

- **Super Admin**: Full system access and user management
- **Developer**: Technical administration and debugging tools
- **Marketing**: Analytics and marketing data access
- **Sales**: Sales analytics and customer data access