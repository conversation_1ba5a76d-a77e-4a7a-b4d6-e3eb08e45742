#!/usr/bin/env php
<?php
/**
 * GuardGeo API Platform - Queue Worker
 * 
 * Command-line script to process background jobs from the queue.
 * 
 * Usage:
 *   php bin/worker.php [options]
 * 
 * Options:
 *   --queue=name     Queue name to process (default: default)
 *   --timeout=secs   Maximum execution time in seconds (default: 3600)
 *   --memory=bytes   Memory limit in bytes (default: 134217728)
 *   --sleep=secs     Sleep time between queue checks (default: 5)
 *   --daemon         Run as daemon (keeps running)
 *   --once           Process one job and exit
 *   --help           Show this help message
 * 
 * Examples:
 *   php bin/worker.php --queue=webhooks --daemon
 *   php bin/worker.php --queue=default --once
 *   php bin/worker.php --timeout=7200 --memory=268435456
 * 
 * @package GuardGeo API Platform
 * <AUTHOR> <PERSON>
 */

// Prevent direct web access
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from the command line.');
}

// Set up paths
define('ABSPATH', dirname(__DIR__) . '/');

// Autoload dependencies
require_once ABSPATH . 'vendor/autoload.php';

// Load configuration
require_once ABSPATH . 'src/Config/Database.php';

use Skpassegna\GuardgeoApi\Config\Database;
use Skpassegna\GuardgeoApi\Core\Worker;
use Skpassegna\GuardgeoApi\Core\Queue;

// Initialize configuration
Database::initialize();

/**
 * Parse command line arguments
 */
function parseArguments(): array
{
    global $argv;
    
    $options = [
        'queue' => 'default',
        'timeout' => 3600,
        'memory' => 134217728, // 128MB
        'sleep' => 5,
        'daemon' => false,
        'once' => false,
        'help' => false
    ];
    
    for ($i = 1; $i < count($argv); $i++) {
        $arg = $argv[$i];
        
        if ($arg === '--help' || $arg === '-h') {
            $options['help'] = true;
        } elseif ($arg === '--daemon' || $arg === '-d') {
            $options['daemon'] = true;
        } elseif ($arg === '--once' || $arg === '-o') {
            $options['once'] = true;
        } elseif (strpos($arg, '--queue=') === 0) {
            $options['queue'] = substr($arg, 8);
        } elseif (strpos($arg, '--timeout=') === 0) {
            $options['timeout'] = (int)substr($arg, 10);
        } elseif (strpos($arg, '--memory=') === 0) {
            $options['memory'] = (int)substr($arg, 9);
        } elseif (strpos($arg, '--sleep=') === 0) {
            $options['sleep'] = (int)substr($arg, 8);
        }
    }
    
    return $options;
}

/**
 * Show help message
 */
function showHelp(): void
{
    echo "GuardGeo API Platform - Queue Worker\n\n";
    echo "Usage: php bin/worker.php [options]\n\n";
    echo "Options:\n";
    echo "  --queue=name     Queue name to process (default: default)\n";
    echo "  --timeout=secs   Maximum execution time in seconds (default: 3600)\n";
    echo "  --memory=bytes   Memory limit in bytes (default: 134217728)\n";
    echo "  --sleep=secs     Sleep time between queue checks (default: 5)\n";
    echo "  --daemon         Run as daemon (keeps running)\n";
    echo "  --once           Process one job and exit\n";
    echo "  --help           Show this help message\n\n";
    echo "Examples:\n";
    echo "  php bin/worker.php --queue=webhooks --daemon\n";
    echo "  php bin/worker.php --queue=default --once\n";
    echo "  php bin/worker.php --timeout=7200 --memory=268435456\n\n";
    echo "Available queues:\n";
    echo "  default          General purpose jobs\n";
    echo "  webhooks         Webhook processing jobs\n";
    echo "  emails           Email sending jobs\n";
    echo "  cleanup          Cleanup and maintenance jobs\n\n";
}

/**
 * Show queue statistics
 */
function showQueueStats(array $queues): void
{
    echo "Queue Statistics:\n";
    echo str_repeat("-", 60) . "\n";
    printf("%-15s %-10s %-10s %-10s %-10s\n", "Queue", "Pending", "Processing", "Failed", "Delayed");
    echo str_repeat("-", 60) . "\n";
    
    foreach ($queues as $queueName) {
        $stats = Queue::getStats($queueName);
        printf("%-15s %-10d %-10d %-10d %-10d\n", 
            $queueName,
            $stats['pending'],
            $stats['processing'],
            $stats['failed'],
            $stats['delayed']
        );
    }
    echo str_repeat("-", 60) . "\n\n";
}

/**
 * Main execution
 */
function main(): void
{
    $options = parseArguments();
    
    if ($options['help']) {
        showHelp();
        return;
    }
    
    // Validate options
    if ($options['timeout'] < 1) {
        echo "Error: Timeout must be at least 1 second\n";
        exit(1);
    }
    
    if ($options['memory'] < 1048576) { // 1MB minimum
        echo "Error: Memory limit must be at least 1MB\n";
        exit(1);
    }
    
    $queues = explode(',', $options['queue']);
    $queues = array_map('trim', $queues);
    
    echo "GuardGeo API Platform - Queue Worker\n";
    echo "====================================\n\n";
    echo "Configuration:\n";
    echo "  Queues: " . implode(', ', $queues) . "\n";
    echo "  Timeout: {$options['timeout']} seconds\n";
    echo "  Memory Limit: " . number_format($options['memory']) . " bytes\n";
    echo "  Mode: " . ($options['once'] ? 'Single Job' : ($options['daemon'] ? 'Daemon' : 'Standard')) . "\n";
    echo "  PID: " . getmypid() . "\n\n";
    
    // Show initial queue statistics
    showQueueStats($queues);
    
    try {
        if ($options['once']) {
            // Process one job and exit
            echo "Processing one job...\n";
            
            $worker = new Worker($queues, $options['timeout'], $options['memory']);
            $jobProcessed = false;
            
            foreach ($queues as $queue) {
                $job = Queue::pop($queue);
                if ($job) {
                    echo "Found job in queue: {$queue}\n";
                    echo "Job ID: {$job['id']}\n";
                    echo "Job Class: {$job['class']}\n";
                    
                    // This would need to be implemented in Worker class
                    // $worker->processJob($job);
                    $jobProcessed = true;
                    break;
                }
            }
            
            if (!$jobProcessed) {
                echo "No jobs found in any queue.\n";
            }
            
        } else {
            // Start worker
            echo "Starting worker...\n";
            echo "Press Ctrl+C to stop gracefully.\n\n";
            
            $worker = new Worker($queues, $options['timeout'], $options['memory']);
            
            // Register shutdown handler
            register_shutdown_function(function() use ($worker) {
                echo "\nShutdown detected. Stopping worker gracefully...\n";
                $worker->stop();
            });
            
            $worker->start();
        }
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
        exit(1);
    }
    
    echo "Worker finished.\n";
}

// Handle errors gracefully
set_error_handler(function($severity, $message, $file, $line) {
    if (error_reporting() & $severity) {
        throw new ErrorException($message, 0, $severity, $file, $line);
    }
});

// Set memory limit
ini_set('memory_limit', '256M');

// Set time limit (will be overridden by worker)
set_time_limit(0);

// Run the worker
main();
