<?php
/**
 * Admin Error View
 * 
 * Displays error messages in the admin interface.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Extract variables
$error = $error ?? 'An error occurred';
$backUrl = $backUrl ?? '/admin';
$errorCode = $errorCode ?? 403;
?>

<div class="error-container">
    <div class="error-icon">
        <?php if ($errorCode == 403): ?>
            🚫
        <?php elseif ($errorCode == 404): ?>
            📄
        <?php elseif ($errorCode >= 500): ?>
            ⚠️
        <?php else: ?>
            ❌
        <?php endif; ?>
    </div>
    
    <h1 class="error-title">
        <?php if ($errorCode == 403): ?>
            Access Denied
        <?php elseif ($errorCode == 404): ?>
            Page Not Found
        <?php elseif ($errorCode >= 500): ?>
            Server Error
        <?php else: ?>
            Error
        <?php endif; ?>
    </h1>
    
    <p class="error-message">
        <?= htmlspecialchars($error) ?>
    </p>
    
    <div class="error-actions">
        <a href="<?= htmlspecialchars($backUrl) ?>" class="btn btn-primary">
            Go Back
        </a>
        <a href="/admin" class="btn btn-secondary">
            Dashboard
        </a>
    </div>
</div>

<style>
.error-container {
    text-align: center;
    padding: 4rem 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 2rem;
    opacity: 0.8;
}

.error-title {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    font-weight: 600;
}

.error-message {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 480px) {
    .error-container {
        padding: 2rem 1rem;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-message {
        font-size: 1rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 100%;
        max-width: 200px;
    }
}
</style>
