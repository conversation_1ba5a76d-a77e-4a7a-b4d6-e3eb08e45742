<?php

namespace Skpassegna\GuardgeoApi\Core;

use Skpassegna\GuardgeoApi\Core\Queue;
use Skpassegna\GuardgeoApi\Jobs\JobInterface;
use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Worker Class
 *
 * Processes jobs from the queue system.
 * Handles job execution, timeouts, and error recovery.
 *
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class Worker
{
    /**
     * @var LoggingService Logging service
     */
    private LoggingService $logger;

    /**
     * @var bool Worker running state
     */
    private bool $running = false;

    /**
     * @var int Maximum execution time in seconds
     */
    private int $maxExecutionTime = 3600; // 1 hour

    /**
     * @var int Sleep time between queue checks in seconds
     */
    private int $sleepTime = 5;

    /**
     * @var int Memory limit in bytes
     */
    private int $memoryLimit = 134217728; // 128MB

    /**
     * @var array Queues to process
     */
    private array $queues = ['default'];

    /**
     * @var int Start time
     */
    private int $startTime;

    /**
     * @var int Jobs processed count
     */
    private int $jobsProcessed = 0;

    /**
     * Constructor
     *
     * @param array $queues Queues to process
     * @param int $maxExecutionTime Maximum execution time
     * @param int $memoryLimit Memory limit in bytes
     */
    public function __construct(array $queues = ['default'], int $maxExecutionTime = 3600, int $memoryLimit = 134217728)
    {
        $this->logger = new LoggingService();
        $this->queues = $queues;
        $this->maxExecutionTime = $maxExecutionTime;
        $this->memoryLimit = $memoryLimit;
        $this->startTime = time();

        // Register signal handlers for graceful shutdown
        if (function_exists('pcntl_signal')) {
            pcntl_signal(SIGTERM, [$this, 'handleSignal']);
            pcntl_signal(SIGINT, [$this, 'handleSignal']);
            pcntl_signal(SIGQUIT, [$this, 'handleSignal']);
        }
    }

    /**
     * Start the worker
     *
     * @return void
     */
    public function start(): void
    {
        $this->running = true;
        $this->logger->logInfo("Worker started", [
            'queues' => $this->queues,
            'max_execution_time' => $this->maxExecutionTime,
            'memory_limit' => $this->memoryLimit,
            'pid' => getmypid()
        ]);

        while ($this->running && $this->shouldContinue()) {
            try {
                // Process delayed jobs first
                $this->processDelayedJobs();

                // Process regular jobs
                $jobProcessed = $this->processNextJob();

                if (!$jobProcessed) {
                    // No jobs available, sleep for a bit
                    sleep($this->sleepTime);
                }

                // Check for signals
                if (function_exists('pcntl_signal_dispatch')) {
                    pcntl_signal_dispatch();
                }

            } catch (\Exception $e) {
                $this->logger->logError("Worker error", [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);

                // Sleep before continuing to avoid rapid error loops
                sleep($this->sleepTime);
            }
        }

        $this->logger->logInfo("Worker stopped", [
            'jobs_processed' => $this->jobsProcessed,
            'runtime' => time() - $this->startTime
        ]);
    }

    /**
     * Stop the worker
     *
     * @return void
     */
    public function stop(): void
    {
        $this->running = false;
        $this->logger->logInfo("Worker stop requested");
    }

    /**
     * Process the next available job
     *
     * @return bool True if a job was processed
     */
    private function processNextJob(): bool
    {
        foreach ($this->queues as $queue) {
            $job = Queue::pop($queue);
            
            if ($job) {
                $this->executeJob($job);
                $this->jobsProcessed++;
                return true;
            }
        }

        return false;
    }

    /**
     * Execute a job
     *
     * @param array $job Job data
     * @return void
     */
    private function executeJob(array $job): void
    {
        $startTime = microtime(true);
        $jobClass = $job['class'];
        $jobData = $job['data'];

        $this->logger->logInfo("Executing job", [
            'job_id' => $job['id'],
            'job_class' => $jobClass,
            'queue' => $job['queue'],
            'attempt' => $job['attempts'] + 1
        ]);

        try {
            // Validate job class
            if (!class_exists($jobClass)) {
                throw new \InvalidArgumentException("Job class does not exist: {$jobClass}");
            }

            $jobInstance = new $jobClass();
            
            if (!$jobInstance instanceof JobInterface) {
                throw new \InvalidArgumentException("Job class must implement JobInterface: {$jobClass}");
            }

            // Set timeout for job execution
            $timeout = $jobInstance->getTimeout();
            if ($timeout > 0) {
                set_time_limit($timeout);
            }

            // Execute the job
            $success = $jobInstance->execute($jobData);

            if ($success) {
                Queue::complete($job);
                
                $executionTime = round((microtime(true) - $startTime) * 1000, 2);
                $this->logger->logInfo("Job completed successfully", [
                    'job_id' => $job['id'],
                    'job_class' => $jobClass,
                    'execution_time_ms' => $executionTime
                ]);
            } else {
                throw new \RuntimeException("Job returned false");
            }

        } catch (\Exception $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            $this->logger->logError("Job execution failed", [
                'job_id' => $job['id'],
                'job_class' => $jobClass,
                'error' => $e->getMessage(),
                'execution_time_ms' => $executionTime,
                'attempt' => $job['attempts'] + 1
            ]);

            // Handle job failure
            Queue::fail($job, $e->getMessage());

            // Call job's failed method if this was the final attempt
            if (isset($jobInstance) && $jobInstance instanceof JobInterface) {
                $maxAttempts = $jobInstance->getMaxAttempts();
                if ($job['attempts'] + 1 >= $maxAttempts) {
                    try {
                        $jobInstance->failed($jobData, $e);
                    } catch (\Exception $failedHandlerException) {
                        $this->logger->logError("Job failed handler error", [
                            'job_id' => $job['id'],
                            'job_class' => $jobClass,
                            'error' => $failedHandlerException->getMessage()
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Process delayed jobs
     *
     * @return void
     */
    private function processDelayedJobs(): void
    {
        foreach ($this->queues as $queue) {
            try {
                $moved = Queue::processDelayedJobs($queue);
                if ($moved > 0) {
                    $this->logger->logInfo("Processed delayed jobs", [
                        'queue' => $queue,
                        'jobs_moved' => $moved
                    ]);
                }
            } catch (\Exception $e) {
                $this->logger->logError("Error processing delayed jobs", [
                    'queue' => $queue,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Check if worker should continue running
     *
     * @return bool True if worker should continue
     */
    private function shouldContinue(): bool
    {
        // Check execution time limit
        if (time() - $this->startTime >= $this->maxExecutionTime) {
            $this->logger->logInfo("Worker stopping due to time limit");
            return false;
        }

        // Check memory usage
        $memoryUsage = memory_get_usage(true);
        if ($memoryUsage >= $this->memoryLimit) {
            $this->logger->logWarning("Worker stopping due to memory limit", [
                'memory_usage' => $memoryUsage,
                'memory_limit' => $this->memoryLimit
            ]);
            return false;
        }

        return true;
    }

    /**
     * Handle system signals
     *
     * @param int $signal Signal number
     * @return void
     */
    public function handleSignal(int $signal): void
    {
        $this->logger->logInfo("Received signal", ['signal' => $signal]);
        
        switch ($signal) {
            case SIGTERM:
            case SIGINT:
            case SIGQUIT:
                $this->stop();
                break;
        }
    }

    /**
     * Get worker statistics
     *
     * @return array Worker statistics
     */
    public function getStats(): array
    {
        return [
            'running' => $this->running,
            'jobs_processed' => $this->jobsProcessed,
            'runtime' => time() - $this->startTime,
            'memory_usage' => memory_get_usage(true),
            'memory_limit' => $this->memoryLimit,
            'queues' => $this->queues,
            'pid' => getmypid()
        ];
    }

    /**
     * Get queue statistics
     *
     * @return array Queue statistics for all queues
     */
    public function getQueueStats(): array
    {
        $stats = [];
        foreach ($this->queues as $queue) {
            $stats[$queue] = Queue::getStats($queue);
        }
        return $stats;
    }
}
