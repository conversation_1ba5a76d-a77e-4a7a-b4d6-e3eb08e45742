<?php

namespace Skpassegna\GuardgeoApi\Jobs;

/**
 * Job Interface
 *
 * Defines the contract for all background jobs.
 *
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
interface JobInterface
{
    /**
     * Execute the job
     *
     * @param array $data Job data
     * @return bool True on success, false on failure
     */
    public function handle(array $data): bool;

    /**
     * Get job timeout in seconds
     *
     * @return int Timeout in seconds
     */
    public function getTimeout(): int;

    /**
     * Get maximum retry attempts
     *
     * @return int Maximum attempts
     */
    public function getMaxAttempts(): int;

    /**
     * Handle job failure
     *
     * @param array $data Job data
     * @param \Exception $exception Exception that caused the failure
     * @return void
     */
    public function failed(array $data, \Exception $exception): void;
}
