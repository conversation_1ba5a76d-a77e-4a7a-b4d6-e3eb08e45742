<?php

namespace Skpassegna\GuardgeoApi\Models;

use Skpassegna\GuardgeoApi\Core\Database;
use PDOException;

/**
 * IpData Model
 * 
 * Manages IP intelligence data from ipRegistry including complete response storage,
 * data freshness checking, and efficient IP lookups using JSONB queries.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class IpData extends BaseModel
{
    /**
     * @var string Table name
     */
    protected string $table = 'ip_data';

    /**
     * @var array Fillable columns for mass assignment
     */
    protected array $fillable = [
        'ip_address',
        'raw_data'
    ];

    /**
     * @var array Columns that should be cast to specific types
     */
    protected array $casts = [
        'raw_data' => 'json'
    ];

    /**
     * Default data freshness period in seconds (3 days)
     */
    const DEFAULT_FRESHNESS_PERIOD = 259200; // 3 days

    /**
     * Find IP data by IP address
     * 
     * @param string $ip IP address
     * @return array|null IP data or null if not found
     */
    public function findByIp(string $ip): ?array
    {
        try {
            $query = "SELECT * FROM {$this->table} WHERE ip_address = :ip LIMIT 1";
            $result = Database::queryOne($query, ['ip' => $ip]);
            
            return $result ? $this->castAttributes($result) : null;
            
        } catch (PDOException $e) {
            error_log("Error finding IP data: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Store complete ipRegistry data for an IP address
     * 
     * @param string $ip IP address
     * @param array $completeIpRegistryData Complete ipRegistry response
     * @return bool True on success
     */
    public function store(string $ip, array $completeIpRegistryData): bool
    {
        try {
            $existingData = $this->findByIp($ip);
            
            $data = [
                'ip_address' => $ip,
                'raw_data' => json_encode($completeIpRegistryData)
            ];
            
            if ($existingData) {
                // Update existing record
                return $this->updateById($existingData['id'], $data);
            } else {
                // Create new record
                return $this->create($data) !== null;
            }
            
        } catch (PDOException $e) {
            error_log("Error storing IP data: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if IP data is fresh (within specified age)
     * 
     * @param string $ip IP address
     * @param int $maxAge Maximum age in seconds (default: 3 days)
     * @return bool True if data is fresh
     */
    public function isDataFresh(string $ip, int $maxAge = self::DEFAULT_FRESHNESS_PERIOD): bool
    {
        try {
            $query = "SELECT updated_at FROM {$this->table} 
                      WHERE ip_address = :ip 
                      AND updated_at > (CURRENT_TIMESTAMP - INTERVAL ':max_age seconds')
                      LIMIT 1";
            
            $result = Database::queryOne($query, [
                'ip' => $ip,
                'max_age' => $maxAge
            ]);
            
            return $result !== null;
            
        } catch (PDOException $e) {
            error_log("Error checking IP data freshness: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get complete ipRegistry data for an IP address
     * 
     * @param string $ip IP address
     * @return array|null Complete ipRegistry response or null if not found
     */
    public function getCompleteData(string $ip): ?array
    {
        $ipData = $this->findByIp($ip);
        
        if (!$ipData || empty($ipData['raw_data'])) {
            return null;
        }
        
        return $ipData['raw_data'];
    }

    /**
     * Get fresh IP data if available
     * 
     * @param string $ip IP address
     * @param int $maxAge Maximum age in seconds (default: 3 days)
     * @return array|null Fresh IP data or null if not available/stale
     */
    public function getFreshData(string $ip, int $maxAge = self::DEFAULT_FRESHNESS_PERIOD): ?array
    {
        if (!$this->isDataFresh($ip, $maxAge)) {
            return null;
        }
        
        return $this->getCompleteData($ip);
    }

    /**
     * Search IP data by location criteria using JSONB queries
     * 
     * @param array $criteria Search criteria (country, city, etc.)
     * @param array $options Query options (limit, offset)
     * @return array Array of matching IP records
     */
    public function searchByLocation(array $criteria, array $options = []): array
    {
        try {
            $whereConditions = [];
            $params = [];
            
            // Build JSONB query conditions
            if (isset($criteria['country_code'])) {
                $whereConditions[] = "raw_data->'location'->'country'->>'code' = :country_code";
                $params['country_code'] = $criteria['country_code'];
            }
            
            if (isset($criteria['country_name'])) {
                $whereConditions[] = "raw_data->'location'->'country'->>'name' ILIKE :country_name";
                $params['country_name'] = '%' . $criteria['country_name'] . '%';
            }
            
            if (isset($criteria['city'])) {
                $whereConditions[] = "raw_data->'location'->>'city' ILIKE :city";
                $params['city'] = '%' . $criteria['city'] . '%';
            }
            
            if (isset($criteria['region'])) {
                $whereConditions[] = "raw_data->'location'->'region'->>'name' ILIKE :region";
                $params['region'] = '%' . $criteria['region'] . '%';
            }
            
            $query = "SELECT * FROM {$this->table}";
            
            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(' AND ', $whereConditions);
            }
            
            // Add ordering
            $query .= " ORDER BY updated_at DESC";
            
            // Add limit and offset
            if (isset($options['limit'])) {
                $query .= " LIMIT " . (int)$options['limit'];
                
                if (isset($options['offset'])) {
                    $query .= " OFFSET " . (int)$options['offset'];
                }
            }
            
            $results = Database::query($query, $params);
            
            return array_map([$this, 'castAttributes'], $results);
            
        } catch (PDOException $e) {
            error_log("Error searching IP data by location: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Search IP data by security criteria using JSONB queries
     * 
     * @param array $criteria Security criteria (is_vpn, is_proxy, etc.)
     * @param array $options Query options (limit, offset)
     * @return array Array of matching IP records
     */
    public function searchBySecurity(array $criteria, array $options = []): array
    {
        try {
            $whereConditions = [];
            $params = [];
            
            // Build JSONB query conditions for security flags
            $securityFlags = [
                'is_vpn', 'is_proxy', 'is_tor', 'is_tor_exit', 'is_relay',
                'is_abuser', 'is_attacker', 'is_bogon', 'is_cloud_provider',
                'is_anonymous', 'is_threat'
            ];
            
            foreach ($securityFlags as $flag) {
                if (isset($criteria[$flag])) {
                    $whereConditions[] = "(raw_data->'security'->>'$flag')::boolean = :$flag";
                    $params[$flag] = (bool)$criteria[$flag];
                }
            }
            
            $query = "SELECT * FROM {$this->table}";
            
            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(' AND ', $whereConditions);
            }
            
            // Add ordering
            $query .= " ORDER BY updated_at DESC";
            
            // Add limit and offset
            if (isset($options['limit'])) {
                $query .= " LIMIT " . (int)$options['limit'];
                
                if (isset($options['offset'])) {
                    $query .= " OFFSET " . (int)$options['offset'];
                }
            }
            
            $results = Database::query($query, $params);
            
            return array_map([$this, 'castAttributes'], $results);
            
        } catch (PDOException $e) {
            error_log("Error searching IP data by security: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get IP data statistics
     * 
     * @return array Statistics about stored IP data
     */
    public function getStatistics(): array
    {
        try {
            $stats = [];
            
            // Total IP records
            $stats['total_ips'] = $this->countBy([]);
            
            // Fresh IP records (within 3 days)
            $freshQuery = "SELECT COUNT(*) as count FROM {$this->table} 
                          WHERE updated_at > (CURRENT_TIMESTAMP - INTERVAL '3 days')";
            $freshResult = Database::queryOne($freshQuery);
            $stats['fresh_ips'] = (int)($freshResult['count'] ?? 0);
            
            // Stale IP records (older than 3 days)
            $stats['stale_ips'] = $stats['total_ips'] - $stats['fresh_ips'];
            
            // VPN/Proxy statistics
            $vpnQuery = "SELECT COUNT(*) as count FROM {$this->table} 
                        WHERE (raw_data->'security'->>'is_vpn')::boolean = true 
                           OR (raw_data->'security'->>'is_proxy')::boolean = true";
            $vpnResult = Database::queryOne($vpnQuery);
            $stats['vpn_proxy_ips'] = (int)($vpnResult['count'] ?? 0);
            
            // Threat statistics
            $threatQuery = "SELECT COUNT(*) as count FROM {$this->table} 
                           WHERE (raw_data->'security'->>'is_threat')::boolean = true";
            $threatResult = Database::queryOne($threatQuery);
            $stats['threat_ips'] = (int)($threatResult['count'] ?? 0);
            
            return $stats;
            
        } catch (PDOException $e) {
            error_log("Error getting IP data statistics: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Clean up stale IP data
     * 
     * @param int $maxAge Maximum age in seconds (default: 30 days)
     * @return int Number of records deleted
     */
    public function cleanupStaleData(int $maxAge = 2592000): int // 30 days default
    {
        try {
            $query = "DELETE FROM {$this->table} 
                      WHERE updated_at < (CURRENT_TIMESTAMP - INTERVAL ':max_age seconds')";
            
            return Database::execute($query, ['max_age' => $maxAge]);
            
        } catch (PDOException $e) {
            error_log("Error cleaning up stale IP data: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get top countries by IP count
     * 
     * @param int $limit Number of countries to return
     * @return array Array of countries with IP counts
     */
    public function getTopCountries(int $limit = 10): array
    {
        try {
            $query = "SELECT 
                        raw_data->'location'->'country'->>'name' as country_name,
                        raw_data->'location'->'country'->>'code' as country_code,
                        COUNT(*) as ip_count
                      FROM {$this->table}
                      WHERE raw_data->'location'->'country'->>'name' IS NOT NULL
                      GROUP BY country_name, country_code
                      ORDER BY ip_count DESC
                      LIMIT :limit";
            
            return Database::query($query, ['limit' => $limit]);
            
        } catch (PDOException $e) {
            error_log("Error getting top countries: " . $e->getMessage());
            return [];
        }
    }
}
