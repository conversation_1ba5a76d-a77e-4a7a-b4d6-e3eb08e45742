<?php

namespace Skpassegna\GuardgeoApi\Jobs;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Abstract Job Class
 *
 * Base class for all background jobs with common functionality.
 *
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
abstract class AbstractJob implements JobInterface
{
    /**
     * @var LoggingService Logging service
     */
    protected LoggingService $logger;

    /**
     * @var int Default timeout in seconds
     */
    protected int $timeout = 300; // 5 minutes

    /**
     * @var int Default maximum attempts
     */
    protected int $maxAttempts = 3;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->logger = new LoggingService();
    }

    /**
     * Get job timeout in seconds
     *
     * @return int Timeout in seconds
     */
    public function getTimeout(): int
    {
        return $this->timeout;
    }

    /**
     * Get maximum retry attempts
     *
     * @return int Maximum attempts
     */
    public function getMaxAttempts(): int
    {
        return $this->maxAttempts;
    }

    /**
     * Handle job failure
     *
     * @param array $data Job data
     * @param \Exception $exception Exception that caused the failure
     * @return void
     */
    public function failed(array $data, \Exception $exception): void
    {
        $this->logger->logError('Job failed', [
            'job_class' => static::class,
            'data' => $data,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }

    /**
     * Log job start
     *
     * @param array $data Job data
     * @return void
     */
    protected function logStart(array $data): void
    {
        $this->logger->logInfo('Job started', [
            'job_class' => static::class,
            'data' => $data
        ]);
    }

    /**
     * Log job completion
     *
     * @param array $data Job data
     * @param array $result Job result
     * @return void
     */
    protected function logComplete(array $data, array $result = []): void
    {
        $this->logger->logInfo('Job completed', [
            'job_class' => static::class,
            'data' => $data,
            'result' => $result
        ]);
    }

    /**
     * Log job progress
     *
     * @param string $message Progress message
     * @param array $context Additional context
     * @return void
     */
    protected function logProgress(string $message, array $context = []): void
    {
        $this->logger->logInfo($message, array_merge([
            'job_class' => static::class
        ], $context));
    }

    /**
     * Validate required data fields
     *
     * @param array $data Job data
     * @param array $required Required field names
     * @throws \InvalidArgumentException If required fields are missing
     */
    protected function validateData(array $data, array $required): void
    {
        foreach ($required as $field) {
            if (!isset($data[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }
    }

    /**
     * Get data field with default value
     *
     * @param array $data Job data
     * @param string $key Field key
     * @param mixed $default Default value
     * @return mixed Field value or default
     */
    protected function getData(array $data, string $key, $default = null)
    {
        return $data[$key] ?? $default;
    }

    /**
     * Check if job should be retried based on exception
     *
     * @param \Exception $exception Exception that occurred
     * @return bool True if job should be retried
     */
    protected function shouldRetry(\Exception $exception): bool
    {
        // Don't retry for certain types of exceptions
        if ($exception instanceof \InvalidArgumentException) {
            return false;
        }

        if ($exception instanceof \TypeError) {
            return false;
        }

        // Retry for network errors, temporary failures, etc.
        return true;
    }

    /**
     * Execute job with error handling
     *
     * @param array $data Job data
     * @return bool True on success
     */
    final public function execute(array $data): bool
    {
        try {
            $this->logStart($data);
            $result = $this->handle($data);
            
            if ($result) {
                $this->logComplete($data);
            }
            
            return $result;
        } catch (\Exception $e) {
            $this->logger->logError('Job execution failed', [
                'job_class' => static::class,
                'data' => $data,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            throw $e;
        }
    }
}
