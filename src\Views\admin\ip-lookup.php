<?php
/**
 * Admin IP Lookup View
 * 
 * Manual IP lookup interface with ipRegistry integration,
 * IP data refresh tools, and cache management.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Extract variables
$lookupResult = $lookupResult ?? null;
$error = $error ?? '';
$success = $success ?? '';
$recentLookups = $recentLookups ?? [];
$cacheStats = $cacheStats ?? [];
?>

<div class="page-header">
    <h1 class="page-title">IP Address Lookup</h1>
    <p class="page-description">Manual IP lookup and cache management tools</p>
</div>

<?php if ($error): ?>
<div class="alert alert-error">
    <?= htmlspecialchars($error) ?>
</div>
<?php endif; ?>

<?php if ($success): ?>
<div class="alert alert-success">
    <?= htmlspecialchars($success) ?>
</div>
<?php endif; ?>

<!-- IP Lookup Form -->
<div class="lookup-section">
    <div class="lookup-form-container">
        <h3>Lookup IP Address</h3>
        <form method="POST" action="/admin/ip-lookup" class="lookup-form" id="lookupForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="ip_address" class="form-label">IP Address</label>
                    <input 
                        type="text" 
                        id="ip_address" 
                        name="ip_address" 
                        class="form-control" 
                        placeholder="Enter IP address (e.g., ***********)"
                        pattern="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$"
                        required
                    >
                    <div class="form-help">
                        Enter a valid IPv4 or IPv6 address to lookup
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary" id="lookupBtn">
                        <span class="btn-text">Lookup IP</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="spinner"></span> Looking up...
                        </span>
                    </button>
                </div>
            </div>
            
            <div class="lookup-options">
                <label class="checkbox-label">
                    <input type="checkbox" name="force_refresh" value="1">
                    <span class="checkmark"></span>
                    Force refresh (bypass cache)
                </label>
                <label class="checkbox-label">
                    <input type="checkbox" name="store_result" value="1" checked>
                    <span class="checkmark"></span>
                    Store result in database
                </label>
            </div>
        </form>
    </div>

    <!-- Cache Statistics -->
    <div class="cache-stats">
        <h4>Cache Statistics</h4>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-label">Total Cached IPs:</span>
                <span class="stat-value"><?= number_format($cacheStats['total_ips'] ?? 0) ?></span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Fresh Records:</span>
                <span class="stat-value"><?= number_format($cacheStats['fresh_records'] ?? 0) ?></span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Stale Records:</span>
                <span class="stat-value"><?= number_format($cacheStats['stale_records'] ?? 0) ?></span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Cache Hit Rate:</span>
                <span class="stat-value"><?= number_format($cacheStats['hit_rate'] ?? 0, 1) ?>%</span>
            </div>
        </div>
    </div>
</div>

<!-- Lookup Result -->
<?php if ($lookupResult): ?>
<div class="result-section">
    <h3>Lookup Result</h3>
    <div class="result-container">
        <div class="result-header">
            <div class="ip-info">
                <h4><?= htmlspecialchars($lookupResult['ip']) ?></h4>
                <span class="ip-type"><?= htmlspecialchars($lookupResult['type'] ?? 'Unknown') ?></span>
                <?php if (isset($lookupResult['hostname'])): ?>
                <span class="hostname"><?= htmlspecialchars($lookupResult['hostname']) ?></span>
                <?php endif; ?>
            </div>
            <div class="result-meta">
                <span class="cache-status <?= $lookupResult['_meta']['from_cache'] ? 'cached' : 'fresh' ?>">
                    <?= $lookupResult['_meta']['from_cache'] ? 'From Cache' : 'Fresh Data' ?>
                </span>
                <span class="lookup-time">
                    Looked up: <?= date('Y-m-d H:i:s') ?>
                </span>
            </div>
        </div>

        <div class="result-tabs">
            <button class="tab-btn active" onclick="showTab('location')">Location</button>
            <button class="tab-btn" onclick="showTab('security')">Security</button>
            <button class="tab-btn" onclick="showTab('connection')">Connection</button>
            <button class="tab-btn" onclick="showTab('raw')">Raw Data</button>
        </div>

        <div class="tab-content">
            <!-- Location Tab -->
            <div id="location-tab" class="tab-pane active">
                <?php if (isset($lookupResult['location'])): ?>
                <div class="info-grid">
                    <div class="info-card">
                        <h5>Country</h5>
                        <div class="country-info">
                            <?php if (isset($lookupResult['location']['country']['flag']['emoji'])): ?>
                            <span class="flag"><?= $lookupResult['location']['country']['flag']['emoji'] ?></span>
                            <?php endif; ?>
                            <span class="country-name"><?= htmlspecialchars($lookupResult['location']['country']['name'] ?? 'Unknown') ?></span>
                            <span class="country-code">(<?= htmlspecialchars($lookupResult['location']['country']['code'] ?? 'XX') ?>)</span>
                        </div>
                        <div class="location-details">
                            <p><strong>Region:</strong> <?= htmlspecialchars($lookupResult['location']['region']['name'] ?? 'Unknown') ?></p>
                            <p><strong>City:</strong> <?= htmlspecialchars($lookupResult['location']['city'] ?? 'Unknown') ?></p>
                            <?php if (isset($lookupResult['location']['postal'])): ?>
                            <p><strong>Postal Code:</strong> <?= htmlspecialchars($lookupResult['location']['postal']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="info-card">
                        <h5>Coordinates</h5>
                        <?php if (isset($lookupResult['location']['latitude'], $lookupResult['location']['longitude'])): ?>
                        <div class="coordinates">
                            <p><strong>Latitude:</strong> <?= number_format($lookupResult['location']['latitude'], 6) ?></p>
                            <p><strong>Longitude:</strong> <?= number_format($lookupResult['location']['longitude'], 6) ?></p>
                        </div>
                        <div class="map-link">
                            <a href="https://www.google.com/maps?q=<?= $lookupResult['location']['latitude'] ?>,<?= $lookupResult['location']['longitude'] ?>" 
                               target="_blank" class="btn btn-small btn-secondary">
                                View on Map
                            </a>
                        </div>
                        <?php else: ?>
                        <p class="no-data">Coordinates not available</p>
                        <?php endif; ?>
                    </div>

                    <div class="info-card">
                        <h5>Timezone</h5>
                        <?php if (isset($lookupResult['time_zone'])): ?>
                        <div class="timezone-info">
                            <p><strong>Timezone:</strong> <?= htmlspecialchars($lookupResult['time_zone']['id'] ?? 'Unknown') ?></p>
                            <p><strong>Current Time:</strong> <?= htmlspecialchars($lookupResult['time_zone']['current_time'] ?? 'Unknown') ?></p>
                            <p><strong>Offset:</strong> <?= isset($lookupResult['time_zone']['offset']) ? gmdate('H:i', abs($lookupResult['time_zone']['offset'])) : 'Unknown' ?></p>
                        </div>
                        <?php else: ?>
                        <p class="no-data">Timezone information not available</p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php else: ?>
                <p class="no-data">Location information not available</p>
                <?php endif; ?>
            </div>

            <!-- Security Tab -->
            <div id="security-tab" class="tab-pane">
                <?php if (isset($lookupResult['security'])): ?>
                <div class="security-grid">
                    <?php foreach ($lookupResult['security'] as $key => $value): ?>
                    <div class="security-item <?= $value ? 'threat' : 'safe' ?>">
                        <div class="security-icon">
                            <?= $value ? '⚠️' : '✅' ?>
                        </div>
                        <div class="security-info">
                            <h6><?= ucwords(str_replace(['is_', '_'], ['', ' '], $key)) ?></h6>
                            <span class="security-status"><?= $value ? 'Detected' : 'Clean' ?></span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <p class="no-data">Security information not available</p>
                <?php endif; ?>
            </div>

            <!-- Connection Tab -->
            <div id="connection-tab" class="tab-pane">
                <?php if (isset($lookupResult['connection']) || isset($lookupResult['company'])): ?>
                <div class="info-grid">
                    <?php if (isset($lookupResult['connection'])): ?>
                    <div class="info-card">
                        <h5>Connection Details</h5>
                        <div class="connection-info">
                            <p><strong>ASN:</strong> <?= htmlspecialchars($lookupResult['connection']['asn'] ?? 'Unknown') ?></p>
                            <p><strong>Organization:</strong> <?= htmlspecialchars($lookupResult['connection']['organization'] ?? 'Unknown') ?></p>
                            <p><strong>Type:</strong> <?= htmlspecialchars($lookupResult['connection']['type'] ?? 'Unknown') ?></p>
                            <?php if (isset($lookupResult['connection']['route'])): ?>
                            <p><strong>Route:</strong> <code><?= htmlspecialchars($lookupResult['connection']['route']) ?></code></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if (isset($lookupResult['company'])): ?>
                    <div class="info-card">
                        <h5>Company Information</h5>
                        <div class="company-info">
                            <p><strong>Name:</strong> <?= htmlspecialchars($lookupResult['company']['name'] ?? 'Unknown') ?></p>
                            <p><strong>Domain:</strong> <?= htmlspecialchars($lookupResult['company']['domain'] ?? 'Unknown') ?></p>
                            <p><strong>Type:</strong> <?= htmlspecialchars($lookupResult['company']['type'] ?? 'Unknown') ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <p class="no-data">Connection information not available</p>
                <?php endif; ?>
            </div>

            <!-- Raw Data Tab -->
            <div id="raw-tab" class="tab-pane">
                <div class="raw-data-container">
                    <div class="raw-data-header">
                        <h5>Complete ipRegistry Response</h5>
                        <button class="btn btn-small btn-secondary" onclick="copyRawData()">Copy JSON</button>
                    </div>
                    <pre class="raw-data" id="rawData"><?= json_encode($lookupResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) ?></pre>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Recent Lookups -->
<?php if (!empty($recentLookups)): ?>
<div class="recent-lookups">
    <h3>Recent Lookups</h3>
    <div class="lookups-table">
        <table class="table">
            <thead>
                <tr>
                    <th>IP Address</th>
                    <th>Country</th>
                    <th>City</th>
                    <th>Security Status</th>
                    <th>Lookup Time</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($recentLookups as $lookup): ?>
                <tr>
                    <td><code><?= htmlspecialchars($lookup['ip_address']) ?></code></td>
                    <td>
                        <?php if (isset($lookup['country_flag'], $lookup['country_name'])): ?>
                        <span class="flag"><?= $lookup['country_flag'] ?></span>
                        <?= htmlspecialchars($lookup['country_name']) ?>
                        <?php else: ?>
                        Unknown
                        <?php endif; ?>

<style>
.lookup-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.lookup-form-container {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.lookup-form-container h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.form-row {
    display: flex;
    gap: 1rem;
    align-items: end;
}

.form-help {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-top: 0.25rem;
}

.lookup-options {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.btn-loading {
    display: none;
}

.cache-stats {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cache-stats h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.stats-grid {
    display: grid;
    gap: 0.75rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.stat-label {
    font-size: 0.9rem;
    color: #555;
}

.stat-value {
    font-weight: 600;
    color: #2c3e50;
}

.result-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.result-section h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.ip-info h4 {
    margin: 0;
    color: #2c3e50;
    font-family: 'Courier New', monospace;
}

.ip-type {
    background: #3498db;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-left: 0.5rem;
}

.hostname {
    display: block;
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-top: 0.25rem;
}

.result-meta {
    text-align: right;
    font-size: 0.9rem;
}

.cache-status {
    display: block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.cache-status.cached {
    background: #fff3cd;
    color: #856404;
}

.cache-status.fresh {
    background: #d4edda;
    color: #155724;
}

.lookup-time {
    color: #7f8c8d;
    font-size: 0.8rem;
}

.result-tabs {
    display: flex;
    border-bottom: 1px solid #eee;
    margin-bottom: 1.5rem;
}

.tab-btn {
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    background: #f8f9fa;
}

.tab-btn.active {
    border-bottom-color: #3498db;
    color: #3498db;
    font-weight: 500;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.info-card {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.info-card h5 {
    margin-bottom: 0.75rem;
    color: #2c3e50;
}

.country-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.flag {
    font-size: 1.5rem;
}

.country-name {
    font-weight: 500;
}

.country-code {
    color: #7f8c8d;
}

.location-details p,
.coordinates p,
.timezone-info p,
.connection-info p,
.company-info p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.map-link {
    margin-top: 0.75rem;
}

.no-data {
    color: #7f8c8d;
    font-style: italic;
    text-align: center;
    padding: 2rem;
}

.security-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.security-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid;
}

.security-item.safe {
    background: #d4edda;
    border-left-color: #27ae60;
}

.security-item.threat {
    background: #f8d7da;
    border-left-color: #e74c3c;
}

.security-icon {
    font-size: 1.5rem;
}

.security-info h6 {
    margin: 0 0 0.25rem 0;
    color: #2c3e50;
}

.security-status {
    font-size: 0.8rem;
    font-weight: 500;
}

.security-item.safe .security-status {
    color: #155724;
}

.security-item.threat .security-status {
    color: #721c24;
}

.raw-data-container {
    background: #f8f9fa;
    border-radius: 6px;
    overflow: hidden;
}

.raw-data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
}

.raw-data-header h5 {
    margin: 0;
    color: #2c3e50;
}

.raw-data {
    padding: 1rem;
    margin: 0;
    background: #f8f9fa;
    font-size: 0.85rem;
    line-height: 1.4;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.recent-lookups {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.recent-lookups h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.security-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.security-badge.safe {
    background: #d4edda;
    color: #155724;
}

.security-badge.threat {
    background: #f8d7da;
    color: #721c24;
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.spinner {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .lookup-section {
        grid-template-columns: 1fr;
    }

    .form-row {
        flex-direction: column;
        align-items: stretch;
    }

    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .result-tabs {
        overflow-x: auto;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .security-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Tab functionality
function showTab(tabName) {
    // Hide all tab panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab pane
    document.getElementById(tabName + '-tab').classList.add('active');

    // Add active class to clicked button
    event.target.classList.add('active');
}

// Form submission with loading state
document.getElementById('lookupForm').addEventListener('submit', function(e) {
    const btn = document.getElementById('lookupBtn');
    const btnText = btn.querySelector('.btn-text');
    const btnLoading = btn.querySelector('.btn-loading');

    // Show loading state
    btnText.style.display = 'none';
    btnLoading.style.display = 'inline-flex';
    btn.disabled = true;
});

// IP address validation
document.getElementById('ip_address').addEventListener('input', function() {
    const ip = this.value;
    const isValid = validateIP(ip);

    if (ip && !isValid) {
        this.style.borderColor = '#e74c3c';
    } else {
        this.style.borderColor = isValid ? '#27ae60' : '#ddd';
    }
});

function validateIP(ip) {
    // IPv4 regex
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

    // IPv6 regex (simplified)
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

// Copy raw data functionality
function copyRawData() {
    const rawData = document.getElementById('rawData');
    const text = rawData.textContent;

    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const btn = event.target;
        const originalText = btn.textContent;
        btn.textContent = 'Copied!';
        btn.style.background = '#27ae60';

        setTimeout(() => {
            btn.textContent = originalText;
            btn.style.background = '';
        }, 2000);
    }).catch(function() {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        alert('Raw data copied to clipboard!');
    });
}

// Quick lookup functionality
function quickLookup(ip) {
    document.getElementById('ip_address').value = ip;
    document.getElementById('lookupForm').submit();
}

// Auto-focus IP input
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('ip_address').focus();
});
</script>
                    </td>
                    <td><?= htmlspecialchars($lookup['city'] ?? 'Unknown') ?></td>
                    <td>
                        <span class="security-badge <?= $lookup['is_threat'] ? 'threat' : 'safe' ?>">
                            <?= $lookup['is_threat'] ? 'Threat' : 'Safe' ?>
                        </span>
                    </td>
                    <td><?= date('Y-m-d H:i:s', strtotime($lookup['created_at'])) ?></td>
                    <td>
                        <button class="btn btn-small btn-info" onclick="quickLookup('<?= htmlspecialchars($lookup['ip_address']) ?>')">
                            View
                        </button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php endif; ?>
