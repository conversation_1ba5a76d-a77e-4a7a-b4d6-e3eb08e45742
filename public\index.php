<?php
/**
 * GuardGeo API Platform - Single Entry Point
 * 
 * This file serves as the single entry point for all requests to the GuardGeo API Platform.
 * It initializes the application, sets up routing, and handles all incoming requests.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__DIR__) . '/');
}

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Autoload dependencies
require_once ABSPATH . 'vendor/autoload.php';

// Load and initialize configuration
require_once ABSPATH . 'src/Config/Database.php';

use Skpassegna\GuardgeoApi\Config\Database;

// Initialize application configuration
Database::initialize();

use Skpassegna\GuardgeoApi\Core\Router;
use Skpassegna\GuardgeoApi\Core\ResponseFormatter;
use Skpassegna\GuardgeoApi\Controllers\AdminController;
use Skpassegna\GuardgeoApi\Controllers\ApiController;
use Skpassegna\GuardgeoApi\Controllers\WebhookController;

try {
    // Initialize router
    $router = new Router();
    
    // Register routes
    
    // Admin routes (web interface)
    $router->addRoute('GET', '/admin', AdminController::class, 'dashboard');
    $router->addRoute('GET', '/admin/login', AdminController::class, 'showLogin');
    $router->addRoute('POST', '/admin/login', AdminController::class, 'login');
    $router->addRoute('GET', '/admin/logout', AdminController::class, 'logout');
    $router->addRoute('GET', '/admin/logs', AdminController::class, 'logs');
    $router->addRoute('GET', '/admin/ip-lookup', AdminController::class, 'ipLookup');
    $router->addRoute('POST', '/admin/ip-lookup', AdminController::class, 'performIpLookup');
    $router->addRoute('GET', '/admin/sync', AdminController::class, 'sync');
    
    // API routes (REST endpoints)
    $router->addRoute('POST', '/api/v1/analyze', ApiController::class, 'analyze');
    $router->addRoute('GET', '/api/v1/health', ApiController::class, 'health');
    
    // Webhook routes
    $router->addRoute('POST', '/webhooks/freemius', WebhookController::class, 'freemius');
    
    // Get request method and URI
    $method = $_SERVER['REQUEST_METHOD'];
    $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    
    // Remove trailing slash except for root
    if ($uri !== '/' && substr($uri, -1) === '/') {
        $uri = rtrim($uri, '/');
    }
    
    // Route the request
    $route = $router->route($method, $uri);
    
    if ($route) {
        $controllerClass = $route['controller'];
        $action = $route['action'];
        $params = $route['params'] ?? [];
        
        // Instantiate controller and call action
        $controller = new $controllerClass();
        
        if (method_exists($controller, $action)) {
            call_user_func_array([$controller, $action], $params);
        } else {
            ResponseFormatter::error('Method not found', 404);
        }
    } else {
        // Handle 404 - route not found
        if (strpos($uri, '/api/') === 0) {
            // API endpoint not found
            ResponseFormatter::error('Endpoint not found', 404);
        } else {
            // Web page not found
            http_response_code(404);
            echo '<!DOCTYPE html>
<html>
<head>
    <title>404 - Page Not Found</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
        h1 { color: #333; }
        p { color: #666; }
    </style>
</head>
<body>
    <h1>404 - Page Not Found</h1>
    <p>The requested page could not be found.</p>
    <a href="/admin">Go to Admin Dashboard</a>
</body>
</html>';
        }
    }
    
} catch (Exception $e) {
    // Log the error
    error_log('GuardGeo API Error: ' . $e->getMessage());
    
    // Return appropriate error response
    if (strpos($_SERVER['REQUEST_URI'], '/api/') === 0) {
        ResponseFormatter::error('Internal server error', 500);
    } else {
        http_response_code(500);
        echo '<!DOCTYPE html>
<html>
<head>
    <title>500 - Internal Server Error</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
        h1 { color: #d32f2f; }
        p { color: #666; }
    </style>
</head>
<body>
    <h1>500 - Internal Server Error</h1>
    <p>An error occurred while processing your request.</p>
</body>
</html>';
    }
}
