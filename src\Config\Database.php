<?php

namespace Skpassegna\GuardgeoApi\Config;

/**
 * Database Configuration
 * 
 * Centralized database configuration for the GuardGeo API Platform.
 * Supports both development and production environments.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class Database
{
    /**
     * Get database configuration
     * 
     * @return array Database configuration array
     */
    public static function getConfig(): array
    {
        // Check if we're in debug mode
        $isDebug = defined('DEBUG') && DEBUG === true;
        
        // Default configuration for shared hosting
        $config = [
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? '5432',
            'database' => $_ENV['DB_NAME'] ?? 'guardgeo_api',
            'username' => $_ENV['DB_USER'] ?? 'guardgeo_user',
            'password' => $_ENV['DB_PASS'] ?? '',
            'charset' => 'utf8',
            'options' => [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false,
                \PDO::ATTR_PERSISTENT => false,
                \PDO::ATTR_TIMEOUT => 30,
            ]
        ];
        
        // Add debug-specific options
        if ($isDebug) {
            $config['options'][\PDO::ATTR_ERRMODE] = \PDO::ERRMODE_EXCEPTION;
        }
        
        return $config;
    }

    /**
     * Get Redis configuration
     * 
     * @return array Redis configuration array
     */
    public static function getRedisConfig(): array
    {
        return [
            'host' => $_ENV['REDIS_HOST'] ?? 'localhost',
            'port' => $_ENV['REDIS_PORT'] ?? 6379,
            'password' => $_ENV['REDIS_PASSWORD'] ?? null,
            'database' => $_ENV['REDIS_DB'] ?? 0,
            'timeout' => 5.0,
            'read_timeout' => 5.0,
            'persistent' => false,
        ];
    }

    /**
     * Get application configuration
     * 
     * @return array Application configuration array
     */
    public static function getAppConfig(): array
    {
        return [
            'debug' => $_ENV['DEBUG'] ?? false,
            'timezone' => $_ENV['TIMEZONE'] ?? 'UTC',
            'log_level' => $_ENV['LOG_LEVEL'] ?? 'info',
            'log_path' => $_ENV['LOG_PATH'] ?? ABSPATH . 'logs/',
            'cache_ttl' => [
                'installation' => 3600, // 1 hour
                'product' => 3600, // 1 hour
                'ip_data' => 259200, // 3 days
            ],
            'freemius' => [
                'api_url' => 'https://api.freemius.com/v1/',
                'webhook_secret' => $_ENV['FREEMIUS_WEBHOOK_SECRET'] ?? '',
            ],
            'ipregistry' => [
                'api_url' => 'https://api.ipregistry.co/',
                'api_key' => $_ENV['IPREGISTRY_API_KEY'] ?? '',
            ],
            'admin' => [
                'allowed_domains' => explode(',', $_ENV['ADMIN_ALLOWED_DOMAINS'] ?? 'skpassegna.me'),
                'session_timeout' => 3600, // 1 hour
                'password_min_length' => 12,
            ],
            'rate_limiting' => [
                'api_requests_per_minute' => 60,
                'webhook_requests_per_minute' => 100,
            ],
        ];
    }

    /**
     * Get Sentry configuration
     * 
     * @return array Sentry configuration array
     */
    public static function getSentryConfig(): array
    {
        return [
            'dsn' => $_ENV['SENTRY_DSN'] ?? null,
            'environment' => $_ENV['SENTRY_ENVIRONMENT'] ?? 'production',
            'release' => $_ENV['SENTRY_RELEASE'] ?? '1.0.0',
            'sample_rate' => (float) ($_ENV['SENTRY_SAMPLE_RATE'] ?? 1.0),
        ];
    }

    /**
     * Load environment variables from .env file if it exists
     * 
     * @return void
     */
    public static function loadEnvironment(): void
    {
        $envFile = ABSPATH . '.env';
        
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            
            foreach ($lines as $line) {
                if (strpos(trim($line), '#') === 0) {
                    continue; // Skip comments
                }
                
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $key = trim($key);
                    $value = trim($value);
                    
                    // Remove quotes if present
                    if (preg_match('/^(["\'])(.*)\\1$/', $value, $matches)) {
                        $value = $matches[2];
                    }
                    
                    $_ENV[$key] = $value;
                    putenv("$key=$value");
                }
            }
        }
    }

    /**
     * Initialize application configuration
     * 
     * @return void
     */
    public static function initialize(): void
    {
        // Load environment variables
        self::loadEnvironment();
        
        // Set timezone
        $config = self::getAppConfig();
        date_default_timezone_set($config['timezone']);
        
        // Set debug mode
        if ($config['debug']) {
            define('DEBUG', true);
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        } else {
            define('DEBUG', false);
            error_reporting(0);
            ini_set('display_errors', 0);
        }
        
        // Ensure log directory exists
        if (!is_dir($config['log_path'])) {
            mkdir($config['log_path'], 0755, true);
        }
    }
}
