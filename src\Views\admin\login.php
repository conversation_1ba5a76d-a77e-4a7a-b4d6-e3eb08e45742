<?php
/**
 * Admin Login View
 * 
 * Login form for admin authentication with email domain validation
 * and password requirements.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Extract variables
$error = $error ?? '';
$email = $email ?? '';
$redirect = $redirect ?? '/admin';
$expired = isset($_GET['expired']) && $_GET['expired'] === '1';
?>

<?php if ($expired): ?>
<div class="alert alert-info">
    <strong>Session Expired</strong><br>
    Your session has expired for security reasons. Please log in again.
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-error">
    <strong>Login Failed</strong><br>
    <?= htmlspecialchars($error) ?>
</div>
<?php endif; ?>

<form method="POST" action="/admin/login" id="loginForm">
    <input type="hidden" name="redirect" value="<?= htmlspecialchars($redirect) ?>">
    
    <div class="form-group">
        <label for="email" class="form-label">Email Address</label>
        <input 
            type="email" 
            id="email" 
            name="email" 
            class="form-control" 
            value="<?= htmlspecialchars($email) ?>"
            required
            autocomplete="email"
            placeholder="Enter your email address"
        >
        <div class="form-help">
            Only authorized domain email addresses are allowed.
        </div>
    </div>

    <div class="form-group">
        <label for="password" class="form-label">Password</label>
        <input 
            type="password" 
            id="password" 
            name="password" 
            class="form-control" 
            required
            autocomplete="current-password"
            placeholder="Enter your password"
            minlength="12"
        >
        <div class="password-requirements">
            <strong>Password Requirements:</strong>
            <ul>
                <li>Minimum 12 characters</li>
                <li>Must be from authorized domain</li>
                <li>Case sensitive</li>
            </ul>
        </div>
    </div>

    <div class="form-group">
        <button type="submit" class="btn" id="submitBtn">
            Sign In
        </button>
    </div>
</form>

<div class="loading" id="loading">
    <div class="spinner"></div>
    <p>Authenticating...</p>
</div>

<div class="login-info">
    <h4>Access Information</h4>
    <p><strong>Authorized Roles:</strong></p>
    <ul>
        <li><strong>Super Admin:</strong> Full system access and user management</li>
        <li><strong>Developer:</strong> Technical administration and debugging</li>
        <li><strong>Marketing:</strong> Analytics and marketing data access</li>
        <li><strong>Sales:</strong> Sales analytics and customer data</li>
    </ul>
    
    <p><strong>Security Features:</strong></p>
    <ul>
        <li>Domain-restricted email authentication</li>
        <li>Session timeout protection</li>
        <li>Activity logging and monitoring</li>
        <li>Role-based access control</li>
    </ul>
</div>

<style>
.form-help {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-top: 0.25rem;
}

.login-info {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
}

.login-info h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.login-info p {
    margin-bottom: 0.5rem;
    color: #555;
    font-size: 0.9rem;
}

.login-info ul {
    margin: 0.5rem 0 1rem 1rem;
    font-size: 0.85rem;
}

.login-info li {
    margin-bottom: 0.25rem;
    color: #666;
}

.login-info strong {
    color: #2c3e50;
}

.form-control:invalid {
    border-color: #e74c3c;
}

.form-control:valid {
    border-color: #27ae60;
}

.password-strength {
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.strength-weak {
    color: #e74c3c;
}

.strength-medium {
    color: #f39c12;
}

.strength-strong {
    color: #27ae60;
}

@media (max-width: 480px) {
    .login-info {
        font-size: 0.8rem;
    }
    
    .login-info ul {
        margin-left: 0.5rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('loginForm');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const submitBtn = document.getElementById('submitBtn');
    const loading = document.getElementById('loading');

    // Email domain validation
    const authorizedDomains = [
        'guardgeo.com',
        'example.com' // Add your authorized domains here
    ];

    function validateEmail(email) {
        const domain = email.split('@')[1];
        return authorizedDomains.includes(domain);
    }

    function validatePassword(password) {
        return password.length >= 12;
    }

    function updateSubmitButton() {
        const emailValid = emailInput.value && validateEmail(emailInput.value);
        const passwordValid = passwordInput.value && validatePassword(passwordInput.value);
        
        submitBtn.disabled = !(emailValid && passwordValid);
        
        if (submitBtn.disabled) {
            submitBtn.style.background = '#bdc3c7';
        } else {
            submitBtn.style.background = '#3498db';
        }
    }

    // Real-time validation
    emailInput.addEventListener('input', function() {
        const email = this.value;
        const isValid = email && validateEmail(email);
        
        if (email && !isValid) {
            this.style.borderColor = '#e74c3c';
            showEmailError('Email domain not authorized');
        } else {
            this.style.borderColor = isValid ? '#27ae60' : '#ddd';
            hideEmailError();
        }
        
        updateSubmitButton();
    });

    passwordInput.addEventListener('input', function() {
        const password = this.value;
        const isValid = validatePassword(password);
        
        if (password && !isValid) {
            this.style.borderColor = '#e74c3c';
        } else {
            this.style.borderColor = isValid ? '#27ae60' : '#ddd';
        }
        
        updateSubmitButton();
    });

    function showEmailError(message) {
        let errorDiv = document.getElementById('email-error');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.id = 'email-error';
            errorDiv.style.cssText = 'color: #e74c3c; font-size: 0.8rem; margin-top: 0.25rem;';
            emailInput.parentNode.appendChild(errorDiv);
        }
        errorDiv.textContent = message;
    }

    function hideEmailError() {
        const errorDiv = document.getElementById('email-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    // Form submission
    form.addEventListener('submit', function(e) {
        const email = emailInput.value;
        const password = passwordInput.value;

        // Final validation
        if (!email || !validateEmail(email)) {
            e.preventDefault();
            emailInput.focus();
            showEmailError('Please enter a valid email from an authorized domain');
            return;
        }

        if (!password || !validatePassword(password)) {
            e.preventDefault();
            passwordInput.focus();
            alert('Password must be at least 12 characters long');
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.textContent = 'Signing in...';
        loading.style.display = 'block';
        
        // Disable form inputs
        emailInput.disabled = true;
        passwordInput.disabled = true;
    });

    // Initial validation
    updateSubmitButton();

    // Auto-focus email field if empty
    if (!emailInput.value) {
        emailInput.focus();
    } else if (!passwordInput.value) {
        passwordInput.focus();
    }

    // Handle browser back button
    window.addEventListener('pageshow', function(event) {
        if (event.persisted) {
            // Reset form state if page was cached
            submitBtn.disabled = false;
            submitBtn.textContent = 'Sign In';
            loading.style.display = 'none';
            emailInput.disabled = false;
            passwordInput.disabled = false;
        }
    });
});
</script>
