<?php

namespace Skpassegna\GuardgeoApi\Services;

use Guz<PERSON>Http\Client;
use GuzzleHttp\Exception\GuzzleException;
use Skpassegna\GuardgeoApi\Models\IpData;
use Skpassegna\GuardgeoApi\Config\Database as DatabaseConfig;

/**
 * IpRegistryService Class
 * 
 * Manages ipRegistry API integration including IP intelligence data fetching,
 * complete response preservation, caching logic, and freshness validation.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class IpRegistryService
{
    /**
     * @var Client HTTP client for ipRegistry API
     */
    private Client $httpClient;

    /**
     * @var IpData IP data model instance
     */
    private IpData $ipDataModel;

    /**
     * @var array Application configuration
     */
    private array $config;

    /**
     * @var string ipRegistry API key
     */
    private string $apiKey;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->config = DatabaseConfig::getAppConfig();
        $this->apiKey = $this->config['ipregistry']['api_key'];
        
        $this->httpClient = new Client([
            'base_uri' => $this->config['ipregistry']['api_url'],
            'timeout' => 30,
            'headers' => [
                'User-Agent' => 'GuardGeo-API/1.0',
                'Accept' => 'application/json'
            ]
        ]);
        
        $this->ipDataModel = new IpData();
    }

    /**
     * Get IP intelligence data with caching and freshness validation
     * 
     * @param string $ip IP address to analyze
     * @return array Complete ipRegistry response with metadata
     */
    public function getIpData(string $ip): array
    {
        // Validate IP address format
        if (!$this->isValidIpAddress($ip)) {
            return [
                'error' => true,
                'message' => 'Invalid IP address format',
                'code' => 'INVALID_IP_FORMAT'
            ];
        }
        
        // Check if we have fresh data in cache/database
        if ($this->isDataFresh($ip)) {
            $cachedData = $this->ipDataModel->getCompleteData($ip);
            
            if ($cachedData) {
                // Add metadata about cache status
                $cachedData['_meta'] = [
                    'cached' => true,
                    'cached_at' => $this->getCachedTimestamp($ip),
                    'fresh_until' => $this->getFreshUntilTimestamp($ip)
                ];
                
                return $cachedData;
            }
        }
        
        // Fetch fresh data from ipRegistry API
        return $this->fetchFreshData($ip);
    }

    /**
     * Check if IP data is fresh (within 3 days)
     * 
     * @param string $ip IP address
     * @return bool True if data is fresh
     */
    public function isDataFresh(string $ip): bool
    {
        return $this->ipDataModel->isDataFresh($ip);
    }

    /**
     * Fetch fresh data from ipRegistry API and store complete response
     * 
     * @param string $ip IP address
     * @return array Complete ipRegistry response with metadata
     */
    public function fetchFreshData(string $ip): array
    {
        try {
            if (empty($this->apiKey)) {
                return [
                    'error' => true,
                    'message' => 'ipRegistry API key not configured',
                    'code' => 'API_KEY_MISSING'
                ];
            }
            
            // Build API URL with all available fields
            $url = $ip . '?key=' . $this->apiKey . '&fields=*';
            
            $response = $this->httpClient->get($url);
            
            if ($response->getStatusCode() === 200) {
                $responseBody = $response->getBody()->getContents();
                $ipData = json_decode($responseBody, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return [
                        'error' => true,
                        'message' => 'Invalid JSON response from ipRegistry',
                        'code' => 'INVALID_JSON_RESPONSE'
                    ];
                }
                
                // Store the complete response in database
                $stored = $this->ipDataModel->store($ip, $ipData);
                
                if (!$stored) {
                    error_log("Failed to store IP data for: $ip");
                }
                
                // Add metadata about freshness
                $ipData['_meta'] = [
                    'cached' => false,
                    'fetched_at' => date('c'),
                    'fresh_until' => date('c', time() + IpData::DEFAULT_FRESHNESS_PERIOD)
                ];
                
                return $ipData;
                
            } else {
                return [
                    'error' => true,
                    'message' => 'ipRegistry API returned error status: ' . $response->getStatusCode(),
                    'code' => 'API_ERROR_' . $response->getStatusCode()
                ];
            }
            
        } catch (GuzzleException $e) {
            error_log("ipRegistry API error: " . $e->getMessage());
            
            return [
                'error' => true,
                'message' => 'Failed to fetch data from ipRegistry API',
                'code' => 'API_REQUEST_FAILED',
                'details' => $e->getMessage()
            ];
        }
    }

    /**
     * Get IP data for multiple IP addresses (batch processing)
     * 
     * @param array $ips Array of IP addresses
     * @return array Array of IP data results
     */
    public function getBatchIpData(array $ips): array
    {
        $results = [];
        
        foreach ($ips as $ip) {
            $results[$ip] = $this->getIpData($ip);
        }
        
        return $results;
    }

    /**
     * Refresh stale IP data
     * 
     * @param string $ip IP address
     * @return array Fresh IP data
     */
    public function refreshIpData(string $ip): array
    {
        return $this->fetchFreshData($ip);
    }

    /**
     * Get security analysis for an IP address
     * 
     * @param string $ip IP address
     * @return array Security analysis data
     */
    public function getSecurityAnalysis(string $ip): array
    {
        $ipData = $this->getIpData($ip);
        
        if (isset($ipData['error'])) {
            return $ipData;
        }
        
        $security = $ipData['security'] ?? [];
        
        // Calculate threat score based on security flags
        $threatScore = 0;
        $threatFactors = [];
        
        if ($security['is_threat'] ?? false) {
            $threatScore += 100;
            $threatFactors[] = 'Known threat';
        }
        
        if ($security['is_abuser'] ?? false) {
            $threatScore += 80;
            $threatFactors[] = 'Known abuser';
        }
        
        if ($security['is_attacker'] ?? false) {
            $threatScore += 90;
            $threatFactors[] = 'Known attacker';
        }
        
        if ($security['is_tor'] ?? false) {
            $threatScore += 60;
            $threatFactors[] = 'Tor network';
        }
        
        if ($security['is_tor_exit'] ?? false) {
            $threatScore += 70;
            $threatFactors[] = 'Tor exit node';
        }
        
        if ($security['is_vpn'] ?? false) {
            $threatScore += 30;
            $threatFactors[] = 'VPN';
        }
        
        if ($security['is_proxy'] ?? false) {
            $threatScore += 40;
            $threatFactors[] = 'Proxy';
        }
        
        if ($security['is_relay'] ?? false) {
            $threatScore += 20;
            $threatFactors[] = 'Relay';
        }
        
        if ($security['is_anonymous'] ?? false) {
            $threatScore += 50;
            $threatFactors[] = 'Anonymous';
        }
        
        // Cap threat score at 100
        $threatScore = min($threatScore, 100);
        
        return [
            'ip' => $ip,
            'threat_score' => $threatScore,
            'threat_level' => $this->getThreatLevel($threatScore),
            'threat_factors' => $threatFactors,
            'security_flags' => $security,
            'recommendation' => $this->getSecurityRecommendation($threatScore),
            'analysis_timestamp' => date('c')
        ];
    }

    /**
     * Get location analysis for an IP address
     * 
     * @param string $ip IP address
     * @return array Location analysis data
     */
    public function getLocationAnalysis(string $ip): array
    {
        $ipData = $this->getIpData($ip);
        
        if (isset($ipData['error'])) {
            return $ipData;
        }
        
        $location = $ipData['location'] ?? [];
        
        return [
            'ip' => $ip,
            'country' => $location['country'] ?? null,
            'region' => $location['region'] ?? null,
            'city' => $location['city'] ?? null,
            'postal' => $location['postal'] ?? null,
            'coordinates' => [
                'latitude' => $location['latitude'] ?? null,
                'longitude' => $location['longitude'] ?? null
            ],
            'timezone' => $ipData['time_zone'] ?? null,
            'currency' => $ipData['currency'] ?? null,
            'language' => $location['language'] ?? null,
            'in_eu' => $location['in_eu'] ?? false,
            'analysis_timestamp' => date('c')
        ];
    }

    /**
     * Validate IP address format
     * 
     * @param string $ip IP address
     * @return bool True if valid
     */
    private function isValidIpAddress(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * Get cached timestamp for an IP
     * 
     * @param string $ip IP address
     * @return string|null Cached timestamp
     */
    private function getCachedTimestamp(string $ip): ?string
    {
        $ipData = $this->ipDataModel->findByIp($ip);
        return $ipData['updated_at'] ?? null;
    }

    /**
     * Get fresh until timestamp for an IP
     * 
     * @param string $ip IP address
     * @return string|null Fresh until timestamp
     */
    private function getFreshUntilTimestamp(string $ip): ?string
    {
        $ipData = $this->ipDataModel->findByIp($ip);
        
        if (!$ipData) {
            return null;
        }
        
        $updatedAt = new \DateTime($ipData['updated_at']);
        $freshUntil = $updatedAt->add(new \DateInterval('P3D')); // Add 3 days
        
        return $freshUntil->format('c');
    }

    /**
     * Get threat level description
     * 
     * @param int $threatScore Threat score (0-100)
     * @return string Threat level
     */
    private function getThreatLevel(int $threatScore): string
    {
        if ($threatScore >= 80) {
            return 'HIGH';
        } elseif ($threatScore >= 50) {
            return 'MEDIUM';
        } elseif ($threatScore >= 20) {
            return 'LOW';
        } else {
            return 'MINIMAL';
        }
    }

    /**
     * Get security recommendation based on threat score
     * 
     * @param int $threatScore Threat score (0-100)
     * @return string Security recommendation
     */
    private function getSecurityRecommendation(int $threatScore): string
    {
        if ($threatScore >= 80) {
            return 'BLOCK - High threat detected, immediate blocking recommended';
        } elseif ($threatScore >= 50) {
            return 'CHALLENGE - Medium threat detected, additional verification recommended';
        } elseif ($threatScore >= 20) {
            return 'MONITOR - Low threat detected, monitoring recommended';
        } else {
            return 'ALLOW - Minimal threat detected, normal processing recommended';
        }
    }

    /**
     * Get service statistics
     * 
     * @return array Service statistics
     */
    public function getStatistics(): array
    {
        return $this->ipDataModel->getStatistics();
    }

    /**
     * Clean up stale IP data
     * 
     * @param int $maxAge Maximum age in seconds
     * @return int Number of records cleaned up
     */
    public function cleanupStaleData(int $maxAge = 2592000): int // 30 days default
    {
        return $this->ipDataModel->cleanupStaleData($maxAge);
    }
}
