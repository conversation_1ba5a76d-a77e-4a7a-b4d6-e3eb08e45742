<?php
/**
 * Admin Queue Management View
 * 
 * Displays queue statistics, job monitoring, and queue management tools.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Extract variables
$queueStats = $queueStats ?? [];
$recentJobs = $recentJobs ?? [];
$failedJobs = $failedJobs ?? [];
$workerStats = $workerStats ?? [];
?>

<div class="page-header">
    <h1 class="page-title">Queue Management</h1>
    <p class="page-description">Monitor background job queues and worker processes</p>
</div>

<!-- Queue Statistics -->
<div class="stats-section">
    <h3>Queue Statistics</h3>
    <div class="queue-stats-grid">
        <?php foreach ($queueStats as $queueName => $stats): ?>
        <div class="queue-stat-card">
            <div class="queue-header">
                <h4><?= htmlspecialchars($queueName) ?></h4>
                <span class="queue-status <?= $stats['pending'] > 0 ? 'active' : 'idle' ?>">
                    <?= $stats['pending'] > 0 ? 'Active' : 'Idle' ?>
                </span>
            </div>
            <div class="queue-metrics">
                <div class="metric">
                    <span class="metric-value"><?= number_format($stats['pending']) ?></span>
                    <span class="metric-label">Pending</span>
                </div>
                <div class="metric">
                    <span class="metric-value"><?= number_format($stats['processing']) ?></span>
                    <span class="metric-label">Processing</span>
                </div>
                <div class="metric">
                    <span class="metric-value"><?= number_format($stats['failed']) ?></span>
                    <span class="metric-label">Failed</span>
                </div>
                <div class="metric">
                    <span class="metric-value"><?= number_format($stats['delayed']) ?></span>
                    <span class="metric-label">Delayed</span>
                </div>
            </div>
            <div class="queue-actions">
                <button class="btn btn-small btn-secondary" onclick="refreshQueue('<?= htmlspecialchars($queueName) ?>')">
                    Refresh
                </button>
                <button class="btn btn-small btn-warning" onclick="clearQueue('<?= htmlspecialchars($queueName) ?>')">
                    Clear
                </button>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>

<!-- Worker Status -->
<?php if (!empty($workerStats)): ?>
<div class="worker-section">
    <h3>Worker Processes</h3>
    <div class="worker-table">
        <table class="table">
            <thead>
                <tr>
                    <th>PID</th>
                    <th>Queues</th>
                    <th>Status</th>
                    <th>Jobs Processed</th>
                    <th>Runtime</th>
                    <th>Memory Usage</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($workerStats as $worker): ?>
                <tr>
                    <td><code><?= $worker['pid'] ?></code></td>
                    <td><?= htmlspecialchars(implode(', ', $worker['queues'])) ?></td>
                    <td>
                        <span class="status-badge <?= $worker['running'] ? 'status-success' : 'status-error' ?>">
                            <?= $worker['running'] ? 'Running' : 'Stopped' ?>
                        </span>
                    </td>
                    <td><?= number_format($worker['jobs_processed']) ?></td>
                    <td><?= gmdate('H:i:s', $worker['runtime']) ?></td>
                    <td><?= \Skpassegna\GuardgeoApi\Core\View::formatFileSize($worker['memory_usage']) ?></td>
                    <td>
                        <?php if ($worker['running']): ?>
                        <button class="btn btn-small btn-danger" onclick="stopWorker(<?= $worker['pid'] ?>)">
                            Stop
                        </button>
                        <?php else: ?>
                        <span class="text-muted">Stopped</span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php endif; ?>

<!-- Recent Jobs -->
<div class="recent-jobs-section">
    <h3>Recent Jobs</h3>
    <div class="jobs-table">
        <?php if (!empty($recentJobs)): ?>
        <table class="table">
            <thead>
                <tr>
                    <th>Job ID</th>
                    <th>Class</th>
                    <th>Queue</th>
                    <th>Status</th>
                    <th>Attempts</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($recentJobs as $job): ?>
                <tr>
                    <td><code><?= htmlspecialchars(substr($job['id'], 0, 12)) ?>...</code></td>
                    <td><?= htmlspecialchars(basename($job['class'])) ?></td>
                    <td><?= htmlspecialchars($job['queue']) ?></td>
                    <td>
                        <?php if ($job['failed_at']): ?>
                        <span class="status-badge status-error">Failed</span>
                        <?php elseif ($job['reserved_at']): ?>
                        <span class="status-badge status-warning">Processing</span>
                        <?php else: ?>
                        <span class="status-badge status-info">Pending</span>
                        <?php endif; ?>
                    </td>
                    <td><?= $job['attempts'] ?>/<?= $job['max_attempts'] ?></td>
                    <td><?= date('Y-m-d H:i:s', $job['created_at']) ?></td>
                    <td>
                        <button class="btn btn-small btn-info" onclick="viewJobDetails('<?= htmlspecialchars($job['id']) ?>')">
                            View
                        </button>
                        <?php if ($job['failed_at']): ?>
                        <button class="btn btn-small btn-success" onclick="retryJob('<?= htmlspecialchars($job['id']) ?>')">
                            Retry
                        </button>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <div class="empty-state">
            <p>No recent jobs to display.</p>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Failed Jobs -->
<?php if (!empty($failedJobs)): ?>
<div class="failed-jobs-section">
    <h3>Failed Jobs</h3>
    <div class="jobs-table">
        <table class="table">
            <thead>
                <tr>
                    <th>Job ID</th>
                    <th>Class</th>
                    <th>Queue</th>
                    <th>Error</th>
                    <th>Failed At</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($failedJobs as $job): ?>
                <tr>
                    <td><code><?= htmlspecialchars(substr($job['id'], 0, 12)) ?>...</code></td>
                    <td><?= htmlspecialchars(basename($job['class'])) ?></td>
                    <td><?= htmlspecialchars($job['queue']) ?></td>
                    <td>
                        <span class="error-message" title="<?= htmlspecialchars($job['error']) ?>">
                            <?= htmlspecialchars(substr($job['error'], 0, 50)) ?>...
                        </span>
                    </td>
                    <td><?= date('Y-m-d H:i:s', $job['failed_at']) ?></td>
                    <td>
                        <button class="btn btn-small btn-info" onclick="viewJobDetails('<?= htmlspecialchars($job['id']) ?>')">
                            View
                        </button>
                        <button class="btn btn-small btn-success" onclick="retryJob('<?= htmlspecialchars($job['id']) ?>')">
                            Retry
                        </button>
                        <button class="btn btn-small btn-danger" onclick="deleteJob('<?= htmlspecialchars($job['id']) ?>')">
                            Delete
                        </button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php endif; ?>

<!-- Job Details Modal -->
<div id="jobModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Job Details</h3>
            <button class="modal-close" onclick="closeJobModal()">&times;</button>
        </div>
        <div class="modal-body" id="jobModalBody">
            <!-- Job details will be loaded here -->
        </div>
    </div>
</div>

<style>
.stats-section {
    margin-bottom: 2rem;
}

.queue-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.queue-stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
}

.queue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.queue-header h4 {
    margin: 0;
    color: #2c3e50;
    text-transform: capitalize;
}

.queue-status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.queue-status.active {
    background: #d4edda;
    color: #155724;
}

.queue-status.idle {
    background: #e2e3e5;
    color: #383d41;
}

.queue-metrics {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.metric {
    text-align: center;
}

.metric-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
}

.metric-label {
    display: block;
    font-size: 0.8rem;
    color: #7f8c8d;
    text-transform: uppercase;
}

.queue-actions {
    display: flex;
    gap: 0.5rem;
}

.worker-section,
.recent-jobs-section,
.failed-jobs-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.worker-section h3,
.recent-jobs-section h3,
.failed-jobs-section h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.error-message {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #e74c3c;
    cursor: help;
}

.text-muted {
    color: #7f8c8d;
}

@media (max-width: 768px) {
    .queue-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .queue-metrics {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .queue-actions {
        flex-direction: column;
    }
}
</style>

<script>
// Auto-refresh queue stats every 30 seconds
setInterval(function() {
    location.reload();
}, 30000);

// Queue management functions
function refreshQueue(queueName) {
    fetch(`/admin/queue/refresh`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ queue: queueName })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to refresh queue: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error refreshing queue: ' + error.message);
    });
}

function clearQueue(queueName) {
    if (!confirm(`Are you sure you want to clear all jobs from the "${queueName}" queue?`)) {
        return;
    }
    
    fetch(`/admin/queue/clear`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ queue: queueName })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to clear queue: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error clearing queue: ' + error.message);
    });
}

function stopWorker(pid) {
    if (!confirm(`Are you sure you want to stop worker process ${pid}?`)) {
        return;
    }
    
    fetch(`/admin/queue/stop-worker`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pid: pid })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to stop worker: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error stopping worker: ' + error.message);
    });
}

function viewJobDetails(jobId) {
    const modal = document.getElementById('jobModal');
    const modalBody = document.getElementById('jobModalBody');
    
    modalBody.innerHTML = '<div class="loading"><div class="spinner"></div><p>Loading job details...</p></div>';
    modal.style.display = 'flex';
    
    fetch(`/admin/queue/job/${jobId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                modalBody.innerHTML = formatJobDetails(data.job);
            } else {
                modalBody.innerHTML = '<div class="alert alert-error">Failed to load job details.</div>';
            }
        })
        .catch(error => {
            modalBody.innerHTML = '<div class="alert alert-error">Error loading job details.</div>';
        });
}

function retryJob(jobId) {
    if (!confirm('Are you sure you want to retry this job?')) {
        return;
    }
    
    fetch(`/admin/queue/retry`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ job_id: jobId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to retry job: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error retrying job: ' + error.message);
    });
}

function deleteJob(jobId) {
    if (!confirm('Are you sure you want to permanently delete this job?')) {
        return;
    }
    
    fetch(`/admin/queue/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ job_id: jobId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to delete job: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error deleting job: ' + error.message);
    });
}

function closeJobModal() {
    document.getElementById('jobModal').style.display = 'none';
}

function formatJobDetails(job) {
    return `
        <div class="job-details">
            <div class="detail-section">
                <h4>Job Information</h4>
                <table class="detail-table">
                    <tr><td><strong>ID:</strong></td><td><code>${job.id}</code></td></tr>
                    <tr><td><strong>Class:</strong></td><td>${job.class}</td></tr>
                    <tr><td><strong>Queue:</strong></td><td>${job.queue}</td></tr>
                    <tr><td><strong>Attempts:</strong></td><td>${job.attempts}/${job.max_attempts}</td></tr>
                    <tr><td><strong>Timeout:</strong></td><td>${job.timeout} seconds</td></tr>
                    <tr><td><strong>Created:</strong></td><td>${new Date(job.created_at * 1000).toLocaleString()}</td></tr>
                    <tr><td><strong>Available:</strong></td><td>${new Date(job.available_at * 1000).toLocaleString()}</td></tr>
                    ${job.reserved_at ? `<tr><td><strong>Reserved:</strong></td><td>${new Date(job.reserved_at * 1000).toLocaleString()}</td></tr>` : ''}
                    ${job.failed_at ? `<tr><td><strong>Failed:</strong></td><td>${new Date(job.failed_at * 1000).toLocaleString()}</td></tr>` : ''}
                </table>
            </div>
            
            <div class="detail-section">
                <h4>Job Data</h4>
                <pre class="json-data">${JSON.stringify(job.data, null, 2)}</pre>
            </div>
            
            ${job.error ? `
            <div class="detail-section">
                <h4>Error Details</h4>
                <pre class="error-data">${job.error}</pre>
            </div>
            ` : ''}
        </div>
    `;
}

// Close modal when clicking outside
document.getElementById('jobModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeJobModal();
    }
});
</script>
