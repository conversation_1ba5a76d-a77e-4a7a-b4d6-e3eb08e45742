<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Core\ResponseFormatter;
use Skpassegna\GuardgeoApi\Core\View;
use Skpassegna\GuardgeoApi\Core\Database;
use Skpassegna\GuardgeoApi\Core\Queue;
use Skpassegna\GuardgeoApi\Services\AuthService;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Services\IpRegistryService;
use Skpassegna\GuardgeoApi\Models\ApiLog;

/**
 * AdminController Class
 *
 * Handles web interface requests for admin users.
 * Provides dashboard, authentication, and management functionality.
 *
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class AdminController
{
    /**
     * @var AuthService Authentication service
     */
    private AuthService $authService;

    /**
     * @var LoggingService Logging service
     */
    private LoggingService $loggingService;

    /**
     * @var IpRegistryService IP registry service
     */
    private IpRegistryService $ipRegistryService;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->authService = new AuthService();
        $this->loggingService = new LoggingService();
        $this->ipRegistryService = new IpRegistryService();

        // Initialize view system
        View::initialize();
    }
    /**
     * Display admin dashboard
     *
     * @return void
     */
    public function dashboard(): void
    {
        try {
            // Require authentication
            $user = $this->authService->requireAuth();

            // Log admin access
            $this->loggingService->logAdminAction('dashboard_access', $user['email'], [
                'user_id' => $user['id'],
                'role' => $user['role']
            ]);

            // Get dashboard statistics
            $stats = $this->getDashboardStats();
            $recentLogs = $this->getRecentLogs(10);
            $systemInfo = $this->getSystemInfo();

            // Render dashboard view
            View::render('admin/dashboard', [
                'pageTitle' => 'Dashboard',
                'currentPage' => 'dashboard',
                'user' => $user,
                'sessionTimeRemaining' => $this->authService->getSessionTimeRemaining(),
                'stats' => $stats,
                'recentLogs' => $recentLogs,
                'systemInfo' => $systemInfo
            ]);

        } catch (\Exception $e) {
            // Redirect to login if not authenticated
            header('Location: /admin/login?redirect=' . urlencode('/admin'));
            exit;
        }
    }

    /**
     * Render dashboard HTML
     *
     * @param array $user Current user data
     * @return void
     */
    private function renderDashboard(array $user): void
    {
        $userName = htmlspecialchars($user['email']);
        $userRole = htmlspecialchars($user['role']);
        $sessionTimeRemaining = $this->authService->getSessionTimeRemaining();

        echo '<!DOCTYPE html>
<html>
<head>
    <title>GuardGeo API - Admin Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: #f5f5f5; }
        .header { background: #007cba; color: white; padding: 15px 20px; display: flex; justify-content: space-between; align-items: center; }
        .user-info { font-size: 14px; }
        .content { margin: 20px; }
        .card { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .nav { background: white; padding: 15px 20px; margin-bottom: 20px; border-radius: 5px; }
        .nav a { margin-right: 20px; text-decoration: none; color: #007cba; }
        .nav a:hover { text-decoration: underline; }
        .logout-btn { background: #d32f2f; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px; }
        .session-timer { font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>GuardGeo API - Admin Dashboard</h1>
        <div class="user-info">
            Welcome, ' . $userName . ' (' . ucfirst($userRole) . ')
            <br><span class="session-timer">Session expires in: ' . gmdate("H:i:s", $sessionTimeRemaining) . '</span>
        </div>
    </div>

    <div class="content">
        <div class="nav">
            <a href="/admin">Dashboard</a>
            <a href="/admin/logs">View Logs</a>
            <a href="/admin/ip-lookup">IP Lookup</a>
            <a href="/admin/sync">Sync Tools</a>
            <a href="/admin/logout" class="logout-btn">Logout</a>
        </div>

        <div class="card">
            <h3>Dashboard Coming Soon</h3>
            <p>This will display API usage statistics, system health, and management tools.</p>
            <p><strong>Your Role:</strong> ' . ucfirst($userRole) . '</p>
            <p><strong>Permissions:</strong> ' . $this->getUserPermissionsDisplay($user) . '</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Show login form
     *
     * @return void
     */
    public function showLogin(): void
    {
        // Redirect if already authenticated
        if ($this->authService->isAuthenticated()) {
            $redirect = $_GET['redirect'] ?? '/admin';
            header('Location: ' . $redirect);
            exit;
        }

        $error = $_GET['error'] ?? '';
        $email = $_GET['email'] ?? '';
        $redirect = $_GET['redirect'] ?? '/admin';

        // Render login view
        View::render('admin/login', [
            'pageTitle' => 'Login',
            'error' => $error,
            'email' => $email,
            'redirect' => $redirect
        ], 'auth');
    }

    /**
     * Render login form HTML
     *
     * @param string $error Error message to display
     * @param string $email Pre-filled email value
     * @return void
     */
    private function renderLoginForm(string $error = '', string $email = ''): void
    {
        $errorHtml = '';
        if ($error) {
            $errorHtml = '<div class="error">' . htmlspecialchars($error) . '</div>';
        }

        $emailValue = htmlspecialchars($email);

        echo '<!DOCTYPE html>
<html>
<head>
    <title>GuardGeo API - Admin Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: #f5f5f5; }
        .login-container { display: flex; justify-content: center; align-items: center; min-height: 100vh; }
        .login-form { background: white; padding: 40px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); max-width: 400px; width: 100%; }
        .logo { text-align: center; margin-bottom: 30px; color: #007cba; }
        input { width: 100%; padding: 12px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { width: 100%; background: #007cba; color: white; padding: 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        button:hover { background: #005a8b; }
        .error { background: #ffebee; color: #c62828; padding: 10px; border-radius: 4px; margin-bottom: 15px; }
        .info { background: #e3f2fd; color: #1565c0; padding: 10px; border-radius: 4px; margin-bottom: 15px; font-size: 14px; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-form">
            <div class="logo">
                <h1>GuardGeo API</h1>
                <p>Admin Login</p>
            </div>

            ' . $errorHtml . '

            <div class="info">
                Only authorized email domains are allowed to access this system.
            </div>

            <form method="POST" action="/admin/login">
                <input type="email" name="email" placeholder="Email Address" value="' . $emailValue . '" required>
                <input type="password" name="password" placeholder="Password (min 12 characters)" required>
                <input type="hidden" name="redirect" value="' . htmlspecialchars($_GET['redirect'] ?? '/admin') . '">
                <button type="submit">Login</button>
            </form>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Process login request
     *
     * @return void
     */
    public function login(): void
    {
        try {
            // Get form data
            $email = trim($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $redirect = $_POST['redirect'] ?? '/admin';

            // Validate input
            if (empty($email) || empty($password)) {
                $this->redirectToLogin('Email and password are required', $email);
                return;
            }

            // Attempt authentication
            $user = $this->authService->authenticate($email, $password);

            if (!$user) {
                // Log failed login attempt
                $this->loggingService->logAdminAction('login_failed', $email, [
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
                ]);

                $this->redirectToLogin('Invalid email or password', $email);
                return;
            }

            // Create session
            $sessionId = $this->authService->createSession($user);

            // Log successful login
            $this->loggingService->logAdminAction('login_success', $user['email'], [
                'user_id' => $user['id'],
                'role' => $user['role'],
                'session_id' => $sessionId,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);

            // Redirect to intended page
            header('Location: ' . $redirect);
            exit;

        } catch (\Exception $e) {
            error_log('Login error: ' . $e->getMessage());
            $this->redirectToLogin('An error occurred during login. Please try again.');
        }
    }

    /**
     * Redirect to login page with error message
     *
     * @param string $error Error message
     * @param string $email Email to pre-fill
     * @return void
     */
    private function redirectToLogin(string $error, string $email = ''): void
    {
        $params = ['error' => $error];
        if ($email) {
            $params['email'] = $email;
        }

        $queryString = http_build_query($params);
        header('Location: /admin/login?' . $queryString);
        exit;
    }

    /**
     * Process logout request
     *
     * @return void
     */
    public function logout(): void
    {
        try {
            // Get current user for logging
            $user = $this->authService->getCurrentUser();

            if ($user) {
                // Log logout action
                $this->loggingService->logAdminAction('logout', $user['email'], [
                    'user_id' => $user['id'],
                    'role' => $user['role'],
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
            }

            // Destroy session
            $this->authService->destroySession();

        } catch (\Exception $e) {
            error_log('Logout error: ' . $e->getMessage());
        }

        // Redirect to login page
        header('Location: /admin/login');
        exit;
    }

    /**
     * Get user permissions display string
     *
     * @param array $user User data
     * @return string Formatted permissions string
     */
    private function getUserPermissionsDisplay(array $user): string
    {
        $role = $user['role'] ?? '';
        $permissions = \Skpassegna\GuardgeoApi\Models\User::ROLE_PERMISSIONS[$role] ?? [];

        if (empty($permissions)) {
            return 'None';
        }

        $displayPermissions = array_map(function($permission) {
            return ucwords(str_replace('_', ' ', $permission));
        }, $permissions);

        return implode(', ', $displayPermissions);
    }

    /**
     * Require authentication and redirect if not authenticated
     *
     * @return array Current user data
     */
    private function requireAuthOrRedirect(): array
    {
        try {
            return $this->authService->requireAuth();
        } catch (\Exception $e) {
            $currentPath = $_SERVER['REQUEST_URI'] ?? '/admin';
            header('Location: /admin/login?redirect=' . urlencode($currentPath));
            exit;
        }
    }

    /**
     * Display API logs
     *
     * @return void
     */
    public function logs(): void
    {
        $user = $this->requireAuthOrRedirect();

        // Check if user has permission to view logs
        if (!$this->authService->hasPermission('view_logs')) {
            http_response_code(403);
            View::render('admin/error', [
                'pageTitle' => 'Access Denied',
                'error' => 'You do not have permission to view logs.',
                'backUrl' => '/admin'
            ]);
            return;
        }

        // Log access
        $this->loggingService->logAdminAction('logs_access', $user['email'], [
            'user_id' => $user['id'],
            'role' => $user['role']
        ]);

        // Get filters from request
        $filters = [
            'search' => $_GET['search'] ?? '',
            'status' => $_GET['status'] ?? '',
            'method' => $_GET['method'] ?? '',
            'date_from' => $_GET['date_from'] ?? '',
            'date_to' => $_GET['date_to'] ?? ''
        ];

        // Get pagination parameters
        $page = max(1, (int)($_GET['page'] ?? 1));
        $perPage = 25;

        // Get logs with filters and pagination
        $logsData = $this->getFilteredLogs($filters, $page, $perPage);

        // Render logs view
        View::render('admin/logs', [
            'pageTitle' => 'API Logs',
            'currentPage' => 'logs',
            'user' => $user,
            'sessionTimeRemaining' => $this->authService->getSessionTimeRemaining(),
            'logs' => $logsData['logs'],
            'filters' => $filters,
            'totalLogs' => $logsData['total'],
            'currentPage' => $page,
            'totalPages' => $logsData['totalPages'],
            'pagination' => $logsData['pagination']
        ]);
    }

    /**
     * Display IP lookup form
     *
     * @return void
     */
    public function ipLookup(): void
    {
        $user = $this->requireAuthOrRedirect();

        // Check if user has permission for manual IP lookup
        if (!$this->authService->hasPermission('manual_ip_lookup')) {
            http_response_code(403);
            echo '<!DOCTYPE html>
<html>
<head>
    <title>Access Denied</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
        h1 { color: #d32f2f; }
    </style>
</head>
<body>
    <h1>403 - Access Denied</h1>
    <p>You do not have permission to perform IP lookups.</p>
    <a href="/admin">Back to Dashboard</a>
</body>
</html>';
            return;
        }

        echo '<!DOCTYPE html>
<html>
<head>
    <title>GuardGeo API - IP Lookup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: #f5f5f5; }
        .header { background: #007cba; color: white; padding: 15px 20px; }
        .content { margin: 20px; }
        .nav { background: white; padding: 15px 20px; margin-bottom: 20px; border-radius: 5px; }
        .nav a { margin-right: 20px; text-decoration: none; color: #007cba; }
        .card { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }
        input { width: 300px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <h1>IP Lookup</h1>
    </div>

    <div class="content">
        <div class="nav">
            <a href="/admin">Dashboard</a>
            <a href="/admin/logs">View Logs</a>
            <a href="/admin/ip-lookup">IP Lookup</a>
            <a href="/admin/sync">Sync Tools</a>
            <a href="/admin/logout">Logout</a>
        </div>

        <div class="card">
            <h3>Manual IP Lookup</h3>
            <p>Enter an IP address to get intelligence data from ipRegistry.</p>
            <form method="POST" action="/admin/ip-lookup">
                <input type="text" name="ip_address" placeholder="Enter IP address (e.g., *******)" required>
                <button type="submit">Lookup</button>
            </form>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Perform IP lookup
     *
     * @return void
     */
    public function performIpLookup(): void
    {
        $user = $this->requireAuthOrRedirect();

        // Check if user has permission for manual IP lookup
        if (!$this->authService->hasPermission('manual_ip_lookup')) {
            ResponseFormatter::error('Insufficient permissions', 403);
            return;
        }

        try {
            $ipAddress = trim($_POST['ip_address'] ?? '');

            if (empty($ipAddress)) {
                ResponseFormatter::error('IP address is required', 400);
                return;
            }

            // Validate IP address format
            if (!filter_var($ipAddress, FILTER_VALIDATE_IP)) {
                ResponseFormatter::error('Invalid IP address format', 400);
                return;
            }

            // Log the lookup action
            $this->loggingService->logAdminAction('manual_ip_lookup', $user['email'], [
                'user_id' => $user['id'],
                'role' => $user['role'],
                'ip_address' => $ipAddress
            ]);

            // TODO: Implement actual IP lookup using IpRegistryService
            // For now, return a placeholder response
            ResponseFormatter::success([
                'ip' => $ipAddress,
                'message' => 'IP lookup functionality will be implemented in the next phase',
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            error_log('IP lookup error: ' . $e->getMessage());
            ResponseFormatter::error('An error occurred during IP lookup', 500);
        }
    }

    /**
     * Display sync tools
     *
     * @return void
     */
    public function sync(): void
    {
        $user = $this->requireAuthOrRedirect();

        // Check if user has permission for Freemius sync
        if (!$this->authService->hasPermission('freemius_sync')) {
            http_response_code(403);
            echo '<!DOCTYPE html>
<html>
<head>
    <title>Access Denied</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
        h1 { color: #d32f2f; }
    </style>
</head>
<body>
    <h1>403 - Access Denied</h1>
    <p>You do not have permission to access sync tools.</p>
    <a href="/admin">Back to Dashboard</a>
</body>
</html>';
            return;
        }

        echo '<!DOCTYPE html>
<html>
<head>
    <title>GuardGeo API - Sync Tools</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: #f5f5f5; }
        .header { background: #007cba; color: white; padding: 15px 20px; }
        .content { margin: 20px; }
        .nav { background: white; padding: 15px 20px; margin-bottom: 20px; border-radius: 5px; }
        .nav a { margin-right: 20px; text-decoration: none; color: #007cba; }
        .card { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Sync Tools</h1>
    </div>

    <div class="content">
        <div class="nav">
            <a href="/admin">Dashboard</a>
            <a href="/admin/logs">View Logs</a>
            <a href="/admin/ip-lookup">IP Lookup</a>
            <a href="/admin/sync">Sync Tools</a>
            <a href="/admin/logout">Logout</a>
        </div>

        <div class="card">
            <h3>Freemius Data Sync</h3>
            <div class="warning">
                <strong>Warning:</strong> These tools will be implemented in the next phase.
                They will allow manual synchronization of Freemius product and installation data.
            </div>
            <p>Available sync operations:</p>
            <ul>
                <li>Refresh Freemius product data</li>
                <li>Update installation statuses</li>
                <li>Clear cache and force refresh</li>
                <li>Validate webhook configurations</li>
            </ul>
            <button disabled>Sync Products</button>
            <button disabled>Sync Installations</button>
            <button disabled>Clear Cache</button>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get dashboard statistics
     *
     * @return array Dashboard statistics
     */
    private function getDashboardStats(): array
    {
        try {
            // Get total requests
            $totalRequests = Database::queryOne("SELECT COUNT(*) as count FROM api_logs")['count'] ?? 0;

            // Get successful requests
            $successfulRequests = Database::queryOne("SELECT COUNT(*) as count FROM api_logs WHERE response_status >= 200 AND response_status < 300")['count'] ?? 0;

            // Get failed requests
            $failedRequests = Database::queryOne("SELECT COUNT(*) as count FROM api_logs WHERE response_status >= 400")['count'] ?? 0;

            // Get requests today
            $requestsToday = Database::queryOne("SELECT COUNT(*) as count FROM api_logs WHERE DATE(created_at) = CURRENT_DATE")['count'] ?? 0;

            // Get failures today
            $failuresToday = Database::queryOne("SELECT COUNT(*) as count FROM api_logs WHERE DATE(created_at) = CURRENT_DATE AND response_status >= 400")['count'] ?? 0;

            // Get unique IPs
            $uniqueIps = Database::queryOne("SELECT COUNT(DISTINCT ip_address) as count FROM api_logs")['count'] ?? 0;

            // Get new IPs today
            $newIpsToday = Database::queryOne("SELECT COUNT(DISTINCT ip_address) as count FROM api_logs WHERE DATE(created_at) = CURRENT_DATE")['count'] ?? 0;

            // Calculate success rate
            $successRate = $totalRequests > 0 ? ($successfulRequests / $totalRequests) * 100 : 0;

            // Get daily requests for the last 7 days
            $dailyRequests = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $count = Database::queryOne("SELECT COUNT(*) as count FROM api_logs WHERE DATE(created_at) = ?", [$date])['count'] ?? 0;
                $dailyRequests[] = (int)$count;
            }

            // Get status distribution
            $statusDistribution = [];
            $statusResults = Database::query("SELECT response_status, COUNT(*) as count FROM api_logs GROUP BY response_status ORDER BY response_status");
            foreach ($statusResults as $row) {
                $statusDistribution[$row['response_status']] = (int)$row['count'];
            }

            return [
                'total_requests' => (int)$totalRequests,
                'successful_requests' => (int)$successfulRequests,
                'failed_requests' => (int)$failedRequests,
                'requests_today' => (int)$requestsToday,
                'failures_today' => (int)$failuresToday,
                'unique_ips' => (int)$uniqueIps,
                'new_ips_today' => (int)$newIpsToday,
                'success_rate' => round($successRate, 1),
                'daily_requests' => $dailyRequests,
                'status_distribution' => $statusDistribution
            ];

        } catch (\Exception $e) {
            error_log('Error getting dashboard stats: ' . $e->getMessage());
            return [
                'total_requests' => 0,
                'successful_requests' => 0,
                'failed_requests' => 0,
                'requests_today' => 0,
                'failures_today' => 0,
                'unique_ips' => 0,
                'new_ips_today' => 0,
                'success_rate' => 0,
                'daily_requests' => [0,0,0,0,0,0,0],
                'status_distribution' => []
            ];
        }
    }

    /**
     * Get recent API logs
     *
     * @param int $limit Number of logs to retrieve
     * @return array Recent logs
     */
    private function getRecentLogs(int $limit = 10): array
    {
        try {
            return Database::query("SELECT * FROM api_logs ORDER BY created_at DESC LIMIT ?", [$limit]);

        } catch (\Exception $e) {
            error_log('Error getting recent logs: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get system information
     *
     * @return array System information
     */
    private function getSystemInfo(): array
    {
        try {
            // Test database connection
            $databaseConnected = Database::testConnection();

            // Test Redis connection
            $redisConnected = \Skpassegna\GuardgeoApi\Core\Redis::testConnection();

            // Get cached installations count
            $cachedInstallations = 0;
            if ($redisConnected) {
                try {
                    $connection = \Skpassegna\GuardgeoApi\Core\Redis::getConnection();
                    if ($connection) {
                        $keys = $connection->keys('installation:*');
                        $cachedInstallations = count($keys);
                    }
                } catch (\Exception $e) {
                    // Ignore error
                }
            }

            // Get cached IP records count
            $cachedIps = Database::queryOne("SELECT COUNT(*) as count FROM ip_data")['count'] ?? 0;

            // Get last webhook time
            $lastWebhook = null;
            // This would be implemented when webhook logging is added

            return [
                'database_connected' => $databaseConnected,
                'redis_connected' => $redisConnected,
                'cached_installations' => (int)$cachedInstallations,
                'cached_ips' => (int)$cachedIps,
                'last_webhook' => $lastWebhook,
                'uptime' => $this->getSystemUptime()
            ];

        } catch (\Exception $e) {
            error_log('Error getting system info: ' . $e->getMessage());
            return [
                'database_connected' => false,
                'redis_connected' => false,
                'cached_installations' => 0,
                'cached_ips' => 0,
                'last_webhook' => null,
                'uptime' => 'Unknown'
            ];
        }
    }

    /**
     * Get system uptime
     *
     * @return string System uptime
     */
    private function getSystemUptime(): string
    {
        if (function_exists('sys_getloadavg') && is_readable('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $uptime = explode(' ', $uptime)[0];
            $seconds = (int)$uptime;

            $days = floor($seconds / 86400);
            $hours = floor(($seconds % 86400) / 3600);
            $minutes = floor(($seconds % 3600) / 60);

            return "{$days}d {$hours}h {$minutes}m";
        }

        return 'Unknown';
    }

    /**
     * Get filtered logs with pagination
     *
     * @param array $filters Filter parameters
     * @param int $page Current page
     * @param int $perPage Items per page
     * @return array Logs data with pagination info
     */
    private function getFilteredLogs(array $filters, int $page, int $perPage): array
    {
        try {
            // Build WHERE clause
            $whereConditions = [];
            $params = [];

            if (!empty($filters['search'])) {
                $whereConditions[] = "(ip_address::text ILIKE ? OR endpoint ILIKE ? OR request_data::text ILIKE ? OR response_data::text ILIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            if (!empty($filters['status'])) {
                $whereConditions[] = "response_status = ?";
                $params[] = $filters['status'];
            }

            if (!empty($filters['method'])) {
                $whereConditions[] = "method = ?";
                $params[] = $filters['method'];
            }

            if (!empty($filters['date_from'])) {
                $whereConditions[] = "DATE(created_at) >= ?";
                $params[] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $whereConditions[] = "DATE(created_at) <= ?";
                $params[] = $filters['date_to'];
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // Get total count
            $countQuery = "SELECT COUNT(*) as count FROM api_logs {$whereClause}";
            $totalLogs = Database::queryOne($countQuery, $params)['count'] ?? 0;

            // Calculate pagination
            $totalPages = max(1, ceil($totalLogs / $perPage));
            $offset = ($page - 1) * $perPage;

            // Get logs
            $logsQuery = "SELECT * FROM api_logs {$whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $logsParams = array_merge($params, [$perPage, $offset]);
            $logs = Database::query($logsQuery, $logsParams);

            return [
                'logs' => $logs,
                'total' => (int)$totalLogs,
                'totalPages' => $totalPages,
                'pagination' => [
                    'currentPage' => $page,
                    'perPage' => $perPage,
                    'totalPages' => $totalPages,
                    'hasNext' => $page < $totalPages,
                    'hasPrev' => $page > 1
                ]
            ];

        } catch (\Exception $e) {
            error_log('Error getting filtered logs: ' . $e->getMessage());
            return [
                'logs' => [],
                'total' => 0,
                'totalPages' => 1,
                'pagination' => [
                    'currentPage' => 1,
                    'perPage' => $perPage,
                    'totalPages' => 1,
                    'hasNext' => false,
                    'hasPrev' => false
                ]
            ];
        }
    }

    /**
     * Display queue management interface
     *
     * @return void
     */
    public function queue(): void
    {
        $user = $this->requireAuthOrRedirect();

        // Check if user has permission to manage queues
        if (!$this->authService->hasPermission('manage_queues')) {
            http_response_code(403);
            View::render('admin/error', [
                'pageTitle' => 'Access Denied',
                'error' => 'You do not have permission to manage queues.',
                'backUrl' => '/admin'
            ]);
            return;
        }

        // Log access
        $this->loggingService->logAdminAction('queue_access', $user['email'], [
            'user_id' => $user['id'],
            'role' => $user['role']
        ]);

        // Get queue statistics
        $queues = ['default', 'webhooks', 'emails', 'cleanup'];
        $queueStats = [];
        foreach ($queues as $queue) {
            $queueStats[$queue] = Queue::getStats($queue);
        }

        // Get recent jobs
        $recentJobs = $this->getRecentJobs(20);

        // Get failed jobs
        $failedJobs = $this->getFailedJobs(10);

        // Get worker stats (placeholder - would need actual worker monitoring)
        $workerStats = $this->getWorkerStats();

        // Render queue view
        View::render('admin/queue', [
            'pageTitle' => 'Queue Management',
            'currentPage' => 'queue',
            'user' => $user,
            'sessionTimeRemaining' => $this->authService->getSessionTimeRemaining(),
            'queueStats' => $queueStats,
            'recentJobs' => $recentJobs,
            'failedJobs' => $failedJobs,
            'workerStats' => $workerStats
        ]);
    }

    /**
     * Get recent jobs from database
     *
     * @param int $limit Number of jobs to retrieve
     * @return array Recent jobs
     */
    private function getRecentJobs(int $limit = 20): array
    {
        try {
            return Database::query(
                "SELECT * FROM job_queue ORDER BY created_at DESC LIMIT ?",
                [$limit]
            );
        } catch (\Exception $e) {
            error_log('Error getting recent jobs: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get failed jobs from database
     *
     * @param int $limit Number of jobs to retrieve
     * @return array Failed jobs
     */
    private function getFailedJobs(int $limit = 10): array
    {
        try {
            return Database::query(
                "SELECT * FROM job_queue WHERE failed_at IS NOT NULL ORDER BY failed_at DESC LIMIT ?",
                [$limit]
            );
        } catch (\Exception $e) {
            error_log('Error getting failed jobs: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get worker statistics
     *
     * @return array Worker statistics
     */
    private function getWorkerStats(): array
    {
        // This would need to be implemented with actual worker monitoring
        // For now, return empty array
        return [];
    }
}
