<?php

namespace Skpassegna\GuardgeoApi\Jobs;

use Skpassegna\GuardgeoApi\Jobs\AbstractJob;
use Skpassegna\GuardgeoApi\Services\FreemiusService;
use Skpassegna\GuardgeoApi\Models\Product;
use Skpassegna\GuardgeoApi\Models\Installation;
use Skpassegna\GuardgeoApi\Core\Redis;

/**
 * Process Freemius Webhook Job
 *
 * Processes Freemius webhook events asynchronously.
 * Updates product and installation data based on webhook events.
 *
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class ProcessFreemiusWebhookJob extends AbstractJob
{
    /**
     * @var FreemiusService Freemius service
     */
    private FreemiusService $freemiusService;

    /**
     * @var Product Product model
     */
    private Product $productModel;

    /**
     * @var Installation Installation model
     */
    private Installation $installationModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->freemiusService = new FreemiusService();
        $this->productModel = new Product();
        $this->installationModel = new Installation();
        
        // Webhook processing timeout
        $this->timeout = 120; // 2 minutes
        $this->maxAttempts = 5; // More retries for webhook processing
    }

    /**
     * Handle the webhook processing job
     *
     * @param array $data Job data containing webhook payload
     * @return bool True on success
     */
    public function handle(array $data): bool
    {
        $this->validateData($data, ['type', 'data', 'timestamp']);

        $webhookType = $data['type'];
        $webhookData = $data['data'];
        $timestamp = $data['timestamp'];

        $this->logProgress("Processing webhook", [
            'type' => $webhookType,
            'timestamp' => $timestamp
        ]);

        try {
            switch ($webhookType) {
                case 'install.activated':
                case 'install.deactivated':
                case 'install.uninstalled':
                    return $this->processInstallationEvent($webhookType, $webhookData);

                case 'subscription.created':
                case 'subscription.updated':
                case 'subscription.cancelled':
                case 'subscription.expired':
                    return $this->processSubscriptionEvent($webhookType, $webhookData);

                case 'license.activated':
                case 'license.deactivated':
                case 'license.expired':
                    return $this->processLicenseEvent($webhookType, $webhookData);

                case 'plugin.updated':
                case 'plugin.version.released':
                    return $this->processProductEvent($webhookType, $webhookData);

                case 'user.updated':
                    return $this->processUserEvent($webhookType, $webhookData);

                default:
                    $this->logProgress("Unknown webhook type", ['type' => $webhookType]);
                    return true; // Don't fail for unknown types
            }
        } catch (\Exception $e) {
            $this->logger->logError("Webhook processing failed", [
                'type' => $webhookType,
                'error' => $e->getMessage(),
                'data' => $webhookData
            ]);
            throw $e;
        }
    }

    /**
     * Process installation-related webhook events
     *
     * @param string $type Event type
     * @param array $data Event data
     * @return bool True on success
     */
    private function processInstallationEvent(string $type, array $data): bool
    {
        if (!isset($data['id'])) {
            throw new \InvalidArgumentException("Installation ID missing from webhook data");
        }

        $installationId = $data['id'];
        
        $this->logProgress("Processing installation event", [
            'type' => $type,
            'installation_id' => $installationId
        ]);

        // Update installation data
        $success = $this->installationModel->updateFromWebhook($data);
        
        if ($success) {
            // Invalidate cache for this installation
            $this->invalidateInstallationCache($installationId);
            
            // Update installation status based on event type
            switch ($type) {
                case 'install.activated':
                    $this->installationModel->setActive($installationId, true);
                    break;
                case 'install.deactivated':
                    $this->installationModel->setActive($installationId, false);
                    break;
                case 'install.uninstalled':
                    $this->installationModel->setUninstalled($installationId, true);
                    break;
            }
        }

        return $success;
    }

    /**
     * Process subscription-related webhook events
     *
     * @param string $type Event type
     * @param array $data Event data
     * @return bool True on success
     */
    private function processSubscriptionEvent(string $type, array $data): bool
    {
        if (!isset($data['install_id'])) {
            throw new \InvalidArgumentException("Installation ID missing from subscription webhook data");
        }

        $installationId = $data['install_id'];
        
        $this->logProgress("Processing subscription event", [
            'type' => $type,
            'installation_id' => $installationId,
            'subscription_id' => $data['id'] ?? null
        ]);

        // Update installation with subscription data
        $installationData = [
            'subscription_id' => $data['id'] ?? null,
            'plan_id' => $data['plan_id'] ?? null,
            'is_premium' => in_array($type, ['subscription.created', 'subscription.updated']),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $success = $this->installationModel->updateById($installationId, $installationData);
        
        if ($success) {
            $this->invalidateInstallationCache($installationId);
        }

        return $success;
    }

    /**
     * Process license-related webhook events
     *
     * @param string $type Event type
     * @param array $data Event data
     * @return bool True on success
     */
    private function processLicenseEvent(string $type, array $data): bool
    {
        if (!isset($data['install_id'])) {
            throw new \InvalidArgumentException("Installation ID missing from license webhook data");
        }

        $installationId = $data['install_id'];
        
        $this->logProgress("Processing license event", [
            'type' => $type,
            'installation_id' => $installationId,
            'license_id' => $data['id'] ?? null
        ]);

        // Update installation with license data
        $installationData = [
            'license_id' => $data['id'] ?? null,
            'is_premium' => $type === 'license.activated',
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $success = $this->installationModel->updateById($installationId, $installationData);
        
        if ($success) {
            $this->invalidateInstallationCache($installationId);
        }

        return $success;
    }

    /**
     * Process product-related webhook events
     *
     * @param string $type Event type
     * @param array $data Event data
     * @return bool True on success
     */
    private function processProductEvent(string $type, array $data): bool
    {
        if (!isset($data['id'])) {
            throw new \InvalidArgumentException("Product ID missing from webhook data");
        }

        $productId = $data['id'];
        
        $this->logProgress("Processing product event", [
            'type' => $type,
            'product_id' => $productId
        ]);

        // Update product data
        $success = $this->productModel->updateFromWebhook($data);
        
        if ($success) {
            // Invalidate cache for this product
            $this->invalidateProductCache($productId);
        }

        return $success;
    }

    /**
     * Process user-related webhook events
     *
     * @param string $type Event type
     * @param array $data Event data
     * @return bool True on success
     */
    private function processUserEvent(string $type, array $data): bool
    {
        // For now, just log user events
        // Future implementation could update user data if needed
        
        $this->logProgress("Processing user event", [
            'type' => $type,
            'user_id' => $data['id'] ?? null
        ]);

        return true;
    }

    /**
     * Invalidate installation cache
     *
     * @param int $installationId Installation ID
     * @return void
     */
    private function invalidateInstallationCache(int $installationId): void
    {
        try {
            if (Redis::isAvailable()) {
                $cacheKey = "installation:{$installationId}";
                Redis::delete($cacheKey);
                
                $this->logProgress("Invalidated installation cache", [
                    'installation_id' => $installationId,
                    'cache_key' => $cacheKey
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->logWarning("Failed to invalidate installation cache", [
                'installation_id' => $installationId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Invalidate product cache
     *
     * @param int $productId Product ID
     * @return void
     */
    private function invalidateProductCache(int $productId): void
    {
        try {
            if (Redis::isAvailable()) {
                $cacheKey = "product:{$productId}";
                Redis::delete($cacheKey);
                
                $this->logProgress("Invalidated product cache", [
                    'product_id' => $productId,
                    'cache_key' => $cacheKey
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->logWarning("Failed to invalidate product cache", [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle job failure
     *
     * @param array $data Job data
     * @param \Exception $exception Exception that caused the failure
     * @return void
     */
    public function failed(array $data, \Exception $exception): void
    {
        parent::failed($data, $exception);

        // Log webhook failure for monitoring
        $this->logger->logError("Freemius webhook processing failed permanently", [
            'webhook_type' => $data['type'] ?? 'unknown',
            'webhook_data' => $data['data'] ?? [],
            'error' => $exception->getMessage(),
            'attempts' => $data['attempts'] ?? 0
        ]);

        // Could send alert to monitoring system here
        // Could also store failed webhook for manual review
    }
}
