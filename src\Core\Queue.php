<?php

namespace Skpassegna\GuardgeoApi\Core;

use Skpassegna\GuardgeoApi\Core\Redis;

/**
 * Queue Class
 *
 * Manages job queues using Redis with fallback to database storage.
 * Provides job scheduling, processing, and retry mechanisms.
 *
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class Queue
{
    /**
     * @var string Default queue name
     */
    private const DEFAULT_QUEUE = 'default';

    /**
     * @var string Failed jobs queue
     */
    private const FAILED_QUEUE = 'failed';

    /**
     * @var string Processing queue
     */
    private const PROCESSING_QUEUE = 'processing';

    /**
     * @var int Default job timeout in seconds
     */
    private const DEFAULT_TIMEOUT = 300; // 5 minutes

    /**
     * @var int Maximum retry attempts
     */
    private const MAX_RETRIES = 3;

    /**
     * Push a job to the queue
     *
     * @param string $jobClass Job class name
     * @param array $data Job data
     * @param string $queue Queue name
     * @param int $delay Delay in seconds before job becomes available
     * @return bool True on success
     */
    public static function push(string $jobClass, array $data = [], string $queue = self::DEFAULT_QUEUE, int $delay = 0): bool
    {
        $job = [
            'id' => self::generateJobId(),
            'class' => $jobClass,
            'data' => $data,
            'queue' => $queue,
            'attempts' => 0,
            'max_attempts' => self::MAX_RETRIES,
            'timeout' => self::DEFAULT_TIMEOUT,
            'created_at' => time(),
            'available_at' => time() + $delay,
            'reserved_at' => null,
            'failed_at' => null,
            'error' => null
        ];

        $jobJson = json_encode($job);

        try {
            if (Redis::isAvailable()) {
                // Use Redis for queue storage
                $queueKey = "queue:{$queue}";
                
                if ($delay > 0) {
                    // Use sorted set for delayed jobs
                    $delayedKey = "queue:{$queue}:delayed";
                    return Redis::getConnection()->zadd($delayedKey, $job['available_at'], $jobJson) > 0;
                } else {
                    // Push to regular queue
                    return Redis::listPush($queueKey, $jobJson);
                }
            } else {
                // Fallback to database storage
                return self::pushToDatabase($job);
            }
        } catch (\Exception $e) {
            error_log("Queue push error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Pop a job from the queue
     *
     * @param string $queue Queue name
     * @return array|null Job data or null if no jobs available
     */
    public static function pop(string $queue = self::DEFAULT_QUEUE): ?array
    {
        try {
            if (Redis::isAvailable()) {
                return self::popFromRedis($queue);
            } else {
                return self::popFromDatabase($queue);
            }
        } catch (\Exception $e) {
            error_log("Queue pop error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Mark a job as completed
     *
     * @param array $job Job data
     * @return bool True on success
     */
    public static function complete(array $job): bool
    {
        try {
            if (Redis::isAvailable()) {
                $processingKey = "queue:{$job['queue']}:processing";
                $jobJson = json_encode($job);
                Redis::getConnection()->lrem($processingKey, 1, $jobJson);
            } else {
                Database::execute("DELETE FROM job_queue WHERE id = ?", [$job['id']]);
            }
            
            // Log successful completion
            self::logJobEvent($job, 'completed');
            return true;
        } catch (\Exception $e) {
            error_log("Queue complete error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Mark a job as failed
     *
     * @param array $job Job data
     * @param string $error Error message
     * @return bool True on success
     */
    public static function fail(array $job, string $error): bool
    {
        try {
            $job['failed_at'] = time();
            $job['error'] = $error;
            $job['attempts']++;

            if ($job['attempts'] < $job['max_attempts']) {
                // Retry the job with exponential backoff
                $delay = pow(2, $job['attempts']) * 60; // 2^attempts minutes
                return self::retry($job, $delay);
            } else {
                // Move to failed queue
                return self::moveToFailed($job);
            }
        } catch (\Exception $e) {
            error_log("Queue fail error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Retry a failed job
     *
     * @param array $job Job data
     * @param int $delay Delay before retry in seconds
     * @return bool True on success
     */
    public static function retry(array $job, int $delay = 0): bool
    {
        try {
            // Remove from processing queue
            if (Redis::isAvailable()) {
                $processingKey = "queue:{$job['queue']}:processing";
                $jobJson = json_encode($job);
                Redis::getConnection()->lrem($processingKey, 1, $jobJson);
            }

            // Update job data
            $job['available_at'] = time() + $delay;
            $job['reserved_at'] = null;

            // Push back to queue
            return self::push($job['class'], $job['data'], $job['queue'], $delay);
        } catch (\Exception $e) {
            error_log("Queue retry error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get queue statistics
     *
     * @param string $queue Queue name
     * @return array Queue statistics
     */
    public static function getStats(string $queue = self::DEFAULT_QUEUE): array
    {
        try {
            if (Redis::isAvailable()) {
                return self::getRedisStats($queue);
            } else {
                return self::getDatabaseStats($queue);
            }
        } catch (\Exception $e) {
            error_log("Queue stats error: " . $e->getMessage());
            return [
                'pending' => 0,
                'processing' => 0,
                'failed' => 0,
                'delayed' => 0
            ];
        }
    }

    /**
     * Process delayed jobs
     *
     * @param string $queue Queue name
     * @return int Number of jobs moved
     */
    public static function processDelayedJobs(string $queue = self::DEFAULT_QUEUE): int
    {
        if (!Redis::isAvailable()) {
            return self::processDatabaseDelayedJobs($queue);
        }

        try {
            $delayedKey = "queue:{$queue}:delayed";
            $queueKey = "queue:{$queue}";
            $now = time();
            $moved = 0;

            $redis = Redis::getConnection();
            $jobs = $redis->zrangebyscore($delayedKey, 0, $now);

            foreach ($jobs as $jobJson) {
                // Move from delayed to regular queue
                if ($redis->zrem($delayedKey, $jobJson) > 0) {
                    Redis::listPush($queueKey, $jobJson);
                    $moved++;
                }
            }

            return $moved;
        } catch (\Exception $e) {
            error_log("Process delayed jobs error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Clear all jobs from a queue
     *
     * @param string $queue Queue name
     * @return bool True on success
     */
    public static function clear(string $queue = self::DEFAULT_QUEUE): bool
    {
        try {
            if (Redis::isAvailable()) {
                $redis = Redis::getConnection();
                $redis->del("queue:{$queue}");
                $redis->del("queue:{$queue}:delayed");
                $redis->del("queue:{$queue}:processing");
                return true;
            } else {
                Database::execute("DELETE FROM job_queue WHERE queue = ?", [$queue]);
                return true;
            }
        } catch (\Exception $e) {
            error_log("Queue clear error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate unique job ID
     *
     * @return string Job ID
     */
    private static function generateJobId(): string
    {
        return uniqid('job_', true);
    }

    /**
     * Pop job from Redis
     *
     * @param string $queue Queue name
     * @return array|null Job data
     */
    private static function popFromRedis(string $queue): ?array
    {
        $queueKey = "queue:{$queue}";
        $processingKey = "queue:{$queue}:processing";
        
        $redis = Redis::getConnection();
        
        // Move job from queue to processing with timeout
        $jobJson = $redis->brpoplpush($queueKey, $processingKey, 1);
        
        if (!$jobJson) {
            return null;
        }

        $job = json_decode($jobJson, true);
        if (!$job) {
            return null;
        }

        $job['reserved_at'] = time();
        return $job;
    }

    /**
     * Pop job from database
     *
     * @param string $queue Queue name
     * @return array|null Job data
     */
    private static function popFromDatabase(string $queue): ?array
    {
        try {
            Database::beginTransaction();

            $job = Database::queryOne(
                "SELECT * FROM job_queue WHERE queue = ? AND available_at <= ? AND reserved_at IS NULL ORDER BY created_at ASC LIMIT 1 FOR UPDATE",
                [$queue, time()]
            );

            if (!$job) {
                Database::rollback();
                return null;
            }

            // Mark as reserved
            Database::execute(
                "UPDATE job_queue SET reserved_at = ? WHERE id = ?",
                [time(), $job['id']]
            );

            Database::commit();

            $job['data'] = json_decode($job['data'], true);
            return $job;
        } catch (\Exception $e) {
            Database::rollback();
            error_log("Database pop error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Push job to database
     *
     * @param array $job Job data
     * @return bool True on success
     */
    private static function pushToDatabase(array $job): bool
    {
        try {
            Database::execute(
                "INSERT INTO job_queue (id, class, data, queue, attempts, max_attempts, timeout, created_at, available_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    $job['id'],
                    $job['class'],
                    json_encode($job['data']),
                    $job['queue'],
                    $job['attempts'],
                    $job['max_attempts'],
                    $job['timeout'],
                    $job['created_at'],
                    $job['available_at']
                ]
            );
            return true;
        } catch (\Exception $e) {
            error_log("Database push error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Move job to failed queue
     *
     * @param array $job Job data
     * @return bool True on success
     */
    private static function moveToFailed(array $job): bool
    {
        try {
            if (Redis::isAvailable()) {
                $failedKey = "queue:" . self::FAILED_QUEUE;
                $processingKey = "queue:{$job['queue']}:processing";
                
                $redis = Redis::getConnection();
                $jobJson = json_encode($job);
                
                // Remove from processing and add to failed
                $redis->lrem($processingKey, 1, $jobJson);
                Redis::listPush($failedKey, $jobJson);
            } else {
                Database::execute(
                    "UPDATE job_queue SET failed_at = ?, error = ? WHERE id = ?",
                    [$job['failed_at'], $job['error'], $job['id']]
                );
            }

            self::logJobEvent($job, 'failed', $job['error']);
            return true;
        } catch (\Exception $e) {
            error_log("Move to failed error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get Redis queue statistics
     *
     * @param string $queue Queue name
     * @return array Statistics
     */
    private static function getRedisStats(string $queue): array
    {
        $redis = Redis::getConnection();
        
        return [
            'pending' => $redis->llen("queue:{$queue}"),
            'processing' => $redis->llen("queue:{$queue}:processing"),
            'failed' => $redis->llen("queue:" . self::FAILED_QUEUE),
            'delayed' => $redis->zcard("queue:{$queue}:delayed")
        ];
    }

    /**
     * Get database queue statistics
     *
     * @param string $queue Queue name
     * @return array Statistics
     */
    private static function getDatabaseStats(string $queue): array
    {
        $pending = Database::queryOne("SELECT COUNT(*) as count FROM job_queue WHERE queue = ? AND reserved_at IS NULL AND failed_at IS NULL", [$queue])['count'] ?? 0;
        $processing = Database::queryOne("SELECT COUNT(*) as count FROM job_queue WHERE queue = ? AND reserved_at IS NOT NULL AND failed_at IS NULL", [$queue])['count'] ?? 0;
        $failed = Database::queryOne("SELECT COUNT(*) as count FROM job_queue WHERE queue = ? AND failed_at IS NOT NULL", [$queue])['count'] ?? 0;
        $delayed = Database::queryOne("SELECT COUNT(*) as count FROM job_queue WHERE queue = ? AND available_at > ? AND reserved_at IS NULL", [$queue, time()])['count'] ?? 0;

        return [
            'pending' => (int)$pending,
            'processing' => (int)$processing,
            'failed' => (int)$failed,
            'delayed' => (int)$delayed
        ];
    }

    /**
     * Process delayed jobs from database
     *
     * @param string $queue Queue name
     * @return int Number of jobs processed
     */
    private static function processDatabaseDelayedJobs(string $queue): int
    {
        try {
            return Database::execute(
                "UPDATE job_queue SET available_at = ? WHERE queue = ? AND available_at > ? AND reserved_at IS NULL",
                [time(), $queue, time()]
            );
        } catch (\Exception $e) {
            error_log("Process database delayed jobs error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Log job event
     *
     * @param array $job Job data
     * @param string $event Event type
     * @param string|null $details Additional details
     */
    private static function logJobEvent(array $job, string $event, ?string $details = null): void
    {
        try {
            $logData = [
                'job_id' => $job['id'],
                'job_class' => $job['class'],
                'queue' => $job['queue'],
                'event' => $event,
                'attempts' => $job['attempts'],
                'details' => $details,
                'created_at' => date('Y-m-d H:i:s')
            ];

            error_log("Job {$event}: " . json_encode($logData));
        } catch (\Exception $e) {
            error_log("Job logging error: " . $e->getMessage());
        }
    }
}
