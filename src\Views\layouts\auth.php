<?php
/**
 * Authentication Layout Template
 * 
 * Layout for login and authentication pages.
 * Provides a clean, centered authentication interface.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Extract variables for use in template
$pageTitle = $pageTitle ?? 'Login';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?> - GuardGeo API Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .auth-container {
            background: white;
            padding: 3rem;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            margin: 2rem;
        }

        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo h1 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .logo p {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .btn {
            width: 100%;
            padding: 0.875rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .alert {
            padding: 1rem;
            margin-bottom: 1.5rem;
            border-radius: 6px;
            border-left: 4px solid;
        }

        .alert-error {
            background: #fdf2f2;
            border-color: #e74c3c;
            color: #721c24;
        }

        .alert-success {
            background: #f0f9f0;
            border-color: #27ae60;
            color: #155724;
        }

        .alert-info {
            background: #e8f4fd;
            border-color: #3498db;
            color: #0c5460;
        }

        .form-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #eee;
        }

        .form-footer p {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .password-requirements {
            font-size: 0.8rem;
            color: #7f8c8d;
            margin-top: 0.5rem;
        }

        .password-requirements ul {
            margin: 0.5rem 0 0 1rem;
        }

        .password-requirements li {
            margin-bottom: 0.25rem;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 1rem;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 480px) {
            .auth-container {
                padding: 2rem;
                margin: 1rem;
            }

            .logo h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">
            <h1>GuardGeo</h1>
            <p>API Platform Administration</p>
        </div>

        <?php if (isset($content)): ?>
            <?= $content ?>
        <?php endif; ?>

        <div class="form-footer">
            <p>&copy; <?= date('Y') ?> GuardGeo API Platform</p>
        </div>
    </div>

    <script>
        // Form validation and loading states
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const submitBtn = document.querySelector('.btn[type="submit"]');
            const loading = document.querySelector('.loading');

            if (form && submitBtn) {
                form.addEventListener('submit', function(e) {
                    // Basic validation
                    const email = form.querySelector('input[name="email"]');
                    const password = form.querySelector('input[name="password"]');

                    if (email && !email.value.trim()) {
                        e.preventDefault();
                        alert('Please enter your email address.');
                        email.focus();
                        return;
                    }

                    if (password && password.value.length < 12) {
                        e.preventDefault();
                        alert('Password must be at least 12 characters long.');
                        password.focus();
                        return;
                    }

                    // Show loading state
                    submitBtn.disabled = true;
                    submitBtn.textContent = 'Signing in...';
                    if (loading) {
                        loading.style.display = 'block';
                    }
                });
            }

            // Auto-focus first input
            const firstInput = document.querySelector('input[type="email"], input[type="text"]');
            if (firstInput) {
                firstInput.focus();
            }
        });
    </script>
</body>
</html>
