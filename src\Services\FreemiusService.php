<?php

namespace Skpassegna\GuardgeoApi\Services;

use Guz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Skpassegna\GuardgeoApi\Core\Redis;
use Skpassegna\GuardgeoApi\Models\Product;
use Skpassegna\GuardgeoApi\Models\Installation;
use Skpassegna\GuardgeoApi\Config\Database as DatabaseConfig;

/**
 * FreemiusService Class
 * 
 * Manages Freemius API integration including installation validation,
 * product verification, and caching of Freemius data.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class FreemiusService
{
    /**
     * @var Client HTTP client for Freemius API
     */
    private Client $httpClient;

    /**
     * @var Product Product model instance
     */
    private Product $productModel;

    /**
     * @var Installation Installation model instance
     */
    private Installation $installationModel;

    /**
     * @var array Application configuration
     */
    private array $config;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->httpClient = new Client([
            'base_uri' => 'https://api.freemius.com/v1/',
            'timeout' => 30,
            'headers' => [
                'User-Agent' => 'GuardGeo-API/1.0',
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ]
        ]);
        
        $this->productModel = new Product();
        $this->installationModel = new Installation();
        $this->config = DatabaseConfig::getAppConfig();
    }

    /**
     * Validate installation for API access
     * 
     * @param int $pluginId Plugin ID
     * @param int $installId Installation ID
     * @param string|null $url Site URL (optional)
     * @return array Validation result
     */
    public function validateInstallation(int $pluginId, int $installId, ?string $url = null): array
    {
        // First check product validity
        $productValidation = $this->productModel->validateForApiAccess($pluginId);
        if (!$productValidation['valid']) {
            return $productValidation;
        }
        
        // Check installation validity
        $installationValidation = $this->installationModel->validateForApiAccess(
            $installId, 
            $pluginId, 
            $url
        );
        
        if (!$installationValidation['valid']) {
            return $installationValidation;
        }
        
        // Check if installation has active license (for premium features)
        $hasActiveLicense = $this->installationModel->hasActiveLicense($installId);
        
        // Update last seen timestamp
        $this->installationModel->updateLastSeen($installId);
        
        return [
            'valid' => true,
            'product' => $productValidation['product'],
            'installation' => $installationValidation['installation'],
            'has_active_license' => $hasActiveLicense,
            'is_premium' => $installationValidation['installation']['is_premium']
        ];
    }

    /**
     * Get installation data with caching
     * 
     * @param int $installId Installation ID
     * @return array|null Installation data or null if not found
     */
    public function getInstallationData(int $installId): ?array
    {
        // Try to get from cache first
        $cacheKey = "installation:{$installId}";
        $cachedData = Redis::get($cacheKey);
        
        if ($cachedData !== null) {
            return $cachedData;
        }
        
        // Get from database
        $installation = $this->installationModel->findByFreemiusId($installId);
        
        if ($installation) {
            // Cache the data
            $this->cacheInstallationData($installId, $installation);
            return $installation;
        }
        
        // Try to fetch from Freemius API as fallback
        return $this->fetchInstallationFromApi($installId);
    }

    /**
     * Cache installation data
     * 
     * @param int $installId Installation ID
     * @param array $data Installation data
     * @return void
     */
    public function cacheInstallationData(int $installId, array $data): void
    {
        $cacheKey = "installation:{$installId}";
        $ttl = $this->config['cache_ttl']['installation'];
        
        Redis::set($cacheKey, $data, $ttl);
    }

    /**
     * Get product data with caching
     * 
     * @param int $productId Product ID
     * @return array|null Product data or null if not found
     */
    public function getProductData(int $productId): ?array
    {
        // Try to get from cache first
        $cacheKey = "product:{$productId}";
        $cachedData = Redis::get($cacheKey);
        
        if ($cachedData !== null) {
            return $cachedData;
        }
        
        // Get from database
        $product = $this->productModel->findByFreemiusId($productId);
        
        if ($product) {
            // Cache the data
            $this->cacheProductData($productId, $product);
            return $product;
        }
        
        // Try to fetch from Freemius API as fallback
        return $this->fetchProductFromApi($productId);
    }

    /**
     * Cache product data
     * 
     * @param int $productId Product ID
     * @param array $data Product data
     * @return void
     */
    public function cacheProductData(int $productId, array $data): void
    {
        $cacheKey = "product:{$productId}";
        $ttl = $this->config['cache_ttl']['product'];
        
        Redis::set($cacheKey, $data, $ttl);
    }

    /**
     * Invalidate installation cache
     * 
     * @param int $installId Installation ID
     * @return void
     */
    public function invalidateInstallationCache(int $installId): void
    {
        $cacheKey = "installation:{$installId}";
        Redis::delete($cacheKey);
    }

    /**
     * Invalidate product cache
     * 
     * @param int $productId Product ID
     * @return void
     */
    public function invalidateProductCache(int $productId): void
    {
        $cacheKey = "product:{$productId}";
        Redis::delete($cacheKey);
    }

    /**
     * Fetch installation data from Freemius API
     * 
     * @param int $installId Installation ID
     * @return array|null Installation data or null if not found
     */
    private function fetchInstallationFromApi(int $installId): ?array
    {
        try {
            $response = $this->httpClient->get("installs/{$installId}.json");
            
            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                
                if (isset($data['install'])) {
                    $installationData = $data['install'];
                    
                    // Update database with fresh data
                    $this->installationModel->updateFromWebhook($installationData);
                    
                    // Cache the data
                    $this->cacheInstallationData($installId, $installationData);
                    
                    return $installationData;
                }
            }
            
        } catch (GuzzleException $e) {
            error_log("Failed to fetch installation from Freemius API: " . $e->getMessage());
        }
        
        return null;
    }

    /**
     * Fetch product data from Freemius API
     * 
     * @param int $productId Product ID
     * @return array|null Product data or null if not found
     */
    private function fetchProductFromApi(int $productId): ?array
    {
        try {
            $response = $this->httpClient->get("plugins/{$productId}.json");
            
            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                
                if (isset($data['plugin'])) {
                    $productData = $data['plugin'];
                    
                    // Update database with fresh data
                    $this->productModel->updateFromWebhook($productData);
                    
                    // Cache the data
                    $this->cacheProductData($productId, $productData);
                    
                    return $productData;
                }
            }
            
        } catch (GuzzleException $e) {
            error_log("Failed to fetch product from Freemius API: " . $e->getMessage());
        }
        
        return null;
    }

    /**
     * Sync installation data from Freemius API
     * 
     * @param int $installId Installation ID
     * @return bool True on success
     */
    public function syncInstallation(int $installId): bool
    {
        $data = $this->fetchInstallationFromApi($installId);
        return $data !== null;
    }

    /**
     * Sync product data from Freemius API
     * 
     * @param int $productId Product ID
     * @return bool True on success
     */
    public function syncProduct(int $productId): bool
    {
        $data = $this->fetchProductFromApi($productId);
        return $data !== null;
    }

    /**
     * Get installation statistics
     * 
     * @param int $pluginId Plugin ID
     * @return array Installation statistics
     */
    public function getInstallationStatistics(int $pluginId): array
    {
        return $this->installationModel->getStatistics($pluginId);
    }

    /**
     * Get product statistics
     * 
     * @param int $productId Product ID
     * @return array Product statistics
     */
    public function getProductStatistics(int $productId): array
    {
        return $this->productModel->getStatistics($productId);
    }

    /**
     * Check if installation is eligible for premium features
     * 
     * @param int $installId Installation ID
     * @return bool True if eligible
     */
    public function isEligibleForPremiumFeatures(int $installId): bool
    {
        $installation = $this->getInstallationData($installId);
        
        if (!$installation) {
            return false;
        }
        
        // Check if installation is premium and has active license
        return $installation['is_premium'] && $this->installationModel->hasActiveLicense($installId);
    }

    /**
     * Validate webhook signature
     * 
     * @param string $payload Webhook payload
     * @param string $signature Webhook signature
     * @param string $secret Webhook secret
     * @return bool True if signature is valid
     */
    public function validateWebhookSignature(string $payload, string $signature, string $secret): bool
    {
        $expectedSignature = hash_hmac('sha256', $payload, $secret);
        return hash_equals($expectedSignature, $signature);
    }
}
