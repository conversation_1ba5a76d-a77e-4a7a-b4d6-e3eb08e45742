<?php

namespace Skpassegna\GuardgeoApi\Models;

/**
 * Installation Model
 * 
 * Manages Freemius installation data including site installations, licenses, and subscriptions.
 * Handles installation validation, webhook updates, and license checking.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class Installation extends BaseModel
{
    /**
     * @var string Table name
     */
    protected string $table = 'installations';

    /**
     * @var array Fillable columns for mass assignment
     */
    protected array $fillable = [
        'freemius_id',
        'secret_key',
        'public_key',
        'site_id',
        'plugin_id',
        'user_id',
        'url',
        'title',
        'version',
        'plan_id',
        'license_id',
        'trial_plan_id',
        'trial_ends',
        'subscription_id',
        'gross',
        'country_code',
        'language',
        'platform_version',
        'sdk_version',
        'programming_language_version',
        'is_active',
        'is_disconnected',
        'is_premium',
        'is_uninstalled',
        'is_locked',
        'source',
        'upgraded',
        'last_seen_at',
        'last_served_update_version',
        'is_beta'
    ];

    /**
     * @var array Columns that should be cast to specific types
     */
    protected array $casts = [
        'freemius_id' => 'int',
        'site_id' => 'int',
        'plugin_id' => 'int',
        'user_id' => 'int',
        'plan_id' => 'int',
        'license_id' => 'int',
        'trial_plan_id' => 'int',
        'subscription_id' => 'int',
        'gross' => 'float',
        'is_active' => 'bool',
        'is_disconnected' => 'bool',
        'is_premium' => 'bool',
        'is_uninstalled' => 'bool',
        'is_locked' => 'bool',
        'source' => 'int',
        'trial_ends' => 'datetime',
        'upgraded' => 'datetime',
        'last_seen_at' => 'datetime',
        'is_beta' => 'bool'
    ];

    /**
     * Find installation by Freemius ID
     * 
     * @param int $freemiusId Freemius installation ID
     * @return array|null Installation data or null if not found
     */
    public function findByFreemiusId(int $freemiusId): ?array
    {
        return $this->findOneBy(['freemius_id' => $freemiusId]);
    }

    /**
     * Find installation by plugin ID and site ID
     * 
     * @param int $pluginId Plugin ID
     * @param int $siteId Site ID
     * @return array|null Installation data or null if not found
     */
    public function findByPluginAndSite(int $pluginId, int $siteId): ?array
    {
        return $this->findOneBy([
            'plugin_id' => $pluginId,
            'site_id' => $siteId
        ]);
    }

    /**
     * Find installations by plugin ID
     * 
     * @param int $pluginId Plugin ID
     * @param array $options Query options
     * @return array Array of installations
     */
    public function findByPluginId(int $pluginId, array $options = []): array
    {
        return $this->findBy(['plugin_id' => $pluginId], $options);
    }

    /**
     * Check if installation is valid for API access
     * 
     * @param int $freemiusId Freemius installation ID
     * @param int $pluginId Plugin ID
     * @param string|null $url Site URL (optional validation)
     * @return array Validation result
     */
    public function validateForApiAccess(int $freemiusId, int $pluginId, ?string $url = null): array
    {
        $installation = $this->findByFreemiusId($freemiusId);
        
        if (!$installation) {
            return [
                'valid' => false,
                'reason' => 'Installation not found',
                'code' => 'INSTALLATION_NOT_FOUND'
            ];
        }
        
        // Check if installation belongs to the specified plugin
        if ($installation['plugin_id'] !== $pluginId) {
            return [
                'valid' => false,
                'reason' => 'Installation does not belong to specified plugin',
                'code' => 'INSTALLATION_PLUGIN_MISMATCH'
            ];
        }
        
        // Check if installation is active
        if (!$installation['is_active']) {
            return [
                'valid' => false,
                'reason' => 'Installation is not active',
                'code' => 'INSTALLATION_INACTIVE'
            ];
        }
        
        // Check if installation is uninstalled
        if ($installation['is_uninstalled']) {
            return [
                'valid' => false,
                'reason' => 'Installation has been uninstalled',
                'code' => 'INSTALLATION_UNINSTALLED'
            ];
        }
        
        // Check if installation is disconnected
        if ($installation['is_disconnected']) {
            return [
                'valid' => false,
                'reason' => 'Installation is disconnected',
                'code' => 'INSTALLATION_DISCONNECTED'
            ];
        }
        
        // Optional URL validation
        if ($url !== null && !empty($installation['url'])) {
            $installationUrl = rtrim($installation['url'], '/');
            $providedUrl = rtrim($url, '/');
            
            if ($installationUrl !== $providedUrl) {
                return [
                    'valid' => false,
                    'reason' => 'Installation URL does not match',
                    'code' => 'INSTALLATION_URL_MISMATCH'
                ];
            }
        }
        
        return [
            'valid' => true,
            'installation' => $installation
        ];
    }

    /**
     * Check if installation has an active license
     * 
     * @param int $freemiusId Freemius installation ID
     * @return bool True if has active license
     */
    public function hasActiveLicense(int $freemiusId): bool
    {
        $installation = $this->findByFreemiusId($freemiusId);
        
        if (!$installation) {
            return false;
        }
        
        // Check if installation is premium and has a license
        return $installation['is_premium'] && !empty($installation['license_id']);
    }

    /**
     * Check if installation is in trial period
     * 
     * @param int $freemiusId Freemius installation ID
     * @return bool True if in trial
     */
    public function isInTrial(int $freemiusId): bool
    {
        $installation = $this->findByFreemiusId($freemiusId);
        
        if (!$installation || empty($installation['trial_ends'])) {
            return false;
        }
        
        $trialEnd = new \DateTime($installation['trial_ends']);
        $now = new \DateTime();
        
        return $now < $trialEnd;
    }

    /**
     * Update installation from Freemius webhook data
     * 
     * @param array $webhookData Freemius webhook data
     * @return bool True on success
     */
    public function updateFromWebhook(array $webhookData): bool
    {
        if (!isset($webhookData['id'])) {
            return false;
        }
        
        $freemiusId = (int)$webhookData['id'];
        $existingInstallation = $this->findByFreemiusId($freemiusId);
        
        $installationData = $this->mapWebhookData($webhookData);
        
        if ($existingInstallation) {
            // Update existing installation
            return $this->updateById($existingInstallation['id'], $installationData);
        } else {
            // Create new installation
            $installationData['freemius_id'] = $freemiusId;
            return $this->create($installationData) !== null;
        }
    }

    /**
     * Map Freemius webhook data to installation fields
     *
     * @param array $webhookData Freemius webhook data
     * @return array Mapped installation data
     */
    protected function mapWebhookData(array $webhookData): array
    {
        $mapped = [];

        // Direct field mappings (Freemius field => Database field)
        $fieldMappings = [
            'secret_key' => 'secret_key',
            'public_key' => 'public_key',
            'site_id' => 'site_id',
            'plugin_id' => 'plugin_id',
            'user_id' => 'user_id',
            'url' => 'url',
            'title' => 'title',
            'version' => 'version',
            'plan_id' => 'plan_id',
            'license_id' => 'license_id',
            'trial_plan_id' => 'trial_plan_id',
            'trial_ends' => 'trial_ends',
            'subscription_id' => 'subscription_id',
            'gross' => 'gross',
            'country_code' => 'country_code',
            'language' => 'language',
            'platform_version' => 'platform_version',
            'sdk_version' => 'sdk_version',
            'programming_language_version' => 'programming_language_version',
            'is_active' => 'is_active',
            'is_disconnected' => 'is_disconnected',
            'is_premium' => 'is_premium',
            'is_uninstalled' => 'is_uninstalled',
            'is_locked' => 'is_locked',
            'source' => 'source',
            'upgraded' => 'upgraded',
            'last_seen_at' => 'last_seen_at',
            'last_served_update_version' => 'last_served_update_version',
            'is_beta' => 'is_beta'
        ];

        foreach ($fieldMappings as $webhookField => $dbField) {
            if (isset($webhookData[$webhookField])) {
                $value = $webhookData[$webhookField];

                // Handle type conversions for fields that come as strings but should be integers
                if (in_array($dbField, ['site_id', 'plugin_id', 'user_id', 'plan_id', 'license_id', 'trial_plan_id', 'subscription_id']) && is_string($value)) {
                    $value = $value !== null ? (int)$value : null;
                }

                // Handle gross as float (can come as string, int, or float)
                if ($dbField === 'gross' && !is_null($value)) {
                    $value = (float)$value;
                }

                // Handle source as integer
                if ($dbField === 'source' && is_string($value)) {
                    $value = (int)$value;
                }

                $mapped[$dbField] = $value;
            }
        }

        // Handle timestamp mapping (Freemius uses 'created' and 'updated', we use 'created_at' and 'updated_at')
        if (isset($webhookData['created'])) {
            $mapped['created_at'] = $webhookData['created'];
        }

        if (isset($webhookData['updated'])) {
            $mapped['updated_at'] = $webhookData['updated'];
        }

        return $mapped;
    }

    /**
     * Get active installations for a plugin
     * 
     * @param int $pluginId Plugin ID
     * @return array Array of active installations
     */
    public function getActiveInstallations(int $pluginId): array
    {
        return $this->findBy([
            'plugin_id' => $pluginId,
            'is_active' => true,
            'is_uninstalled' => false
        ], ['order' => 'last_seen_at DESC']);
    }

    /**
     * Get installation statistics for a plugin
     * 
     * @param int $pluginId Plugin ID
     * @return array Installation statistics
     */
    public function getStatistics(int $pluginId): array
    {
        $totalInstalls = $this->countBy(['plugin_id' => $pluginId]);
        $activeInstalls = $this->countBy([
            'plugin_id' => $pluginId,
            'is_active' => true,
            'is_uninstalled' => false
        ]);
        $premiumInstalls = $this->countBy([
            'plugin_id' => $pluginId,
            'is_premium' => true,
            'is_uninstalled' => false
        ]);
        
        return [
            'total_installs' => $totalInstalls,
            'active_installs' => $activeInstalls,
            'premium_installs' => $premiumInstalls,
            'conversion_rate' => $totalInstalls > 0 
                ? round(($premiumInstalls / $totalInstalls) * 100, 2)
                : 0
        ];
    }

    /**
     * Update last seen timestamp
     * 
     * @param int $freemiusId Freemius installation ID
     * @return bool True on success
     */
    public function updateLastSeen(int $freemiusId): bool
    {
        $installation = $this->findByFreemiusId($freemiusId);
        
        if (!$installation) {
            return false;
        }
        
        return $this->updateById($installation['id'], [
            'last_seen_at' => date('Y-m-d H:i:s')
        ]);
    }
}
