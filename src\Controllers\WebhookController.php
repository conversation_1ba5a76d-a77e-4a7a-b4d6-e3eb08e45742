<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Core\ResponseFormatter;
use Skpassegna\GuardgeoApi\Core\Redis;
use Skpassegna\GuardgeoApi\Core\Queue;
use Skpassegna\GuardgeoApi\Services\FreemiusService;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Jobs\ProcessFreemiusWebhookJob;
use Skpassegna\GuardgeoApi\Config\Database as DatabaseConfig;

/**
 * WebhookController Class
 *
 * Handles webhook requests from external services like Freemius.
 * Processes webhook events and updates system data accordingly.
 *
 * @package GuardGeo API Platform
 * <AUTHOR> <PERSON>
 */
class WebhookController
{
    /**
     * @var FreemiusService Freemius service instance
     */
    private FreemiusService $freemiusService;

    /**
     * @var LoggingService Logging service instance
     */
    private LoggingService $loggingService;

    /**
     * @var array Application configuration
     */
    private array $config;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->freemiusService = new FreemiusService();
        $this->loggingService = new LoggingService();
        $this->config = DatabaseConfig::getAppConfig();
    }

    /**
     * Handle Freemius webhook
     *
     * POST /webhooks/freemius
     *
     * @return void
     */
    public function freemius(): void
    {
        $requestId = ResponseFormatter::generateRequestId();

        try {
            // Get raw payload
            $payload = file_get_contents('php://input');

            if (empty($payload)) {
                $this->loggingService->logWebhook('freemius', 'empty_payload', null, 400, $requestId);
                ResponseFormatter::error('Empty payload', 400, 'EMPTY_PAYLOAD', null, $requestId);
                return;
            }

            // Get signature from headers
            $signature = $this->getSignatureFromHeaders();

            if (empty($signature)) {
                $this->loggingService->logWebhook('freemius', 'missing_signature', $payload, 401, $requestId);
                ResponseFormatter::error('Missing signature', 401, 'MISSING_SIGNATURE', null, $requestId);
                return;
            }

            // Validate HMAC signature
            $webhookSecret = $this->config['freemius']['webhook_secret'];

            if (empty($webhookSecret)) {
                $this->loggingService->logWebhook('freemius', 'missing_secret', $payload, 500, $requestId);
                ResponseFormatter::serverError('Webhook secret not configured');
                return;
            }

            if (!$this->freemiusService->validateWebhookSignature($payload, $signature, $webhookSecret)) {
                $this->loggingService->logWebhook('freemius', 'invalid_signature', $payload, 401, $requestId);
                ResponseFormatter::authenticationError('Invalid signature');
                return;
            }

            // Parse JSON payload
            $data = json_decode($payload, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->loggingService->logWebhook('freemius', 'invalid_json', $payload, 400, $requestId);
                ResponseFormatter::error('Invalid JSON payload', 400, 'INVALID_JSON', null, $requestId);
                return;
            }

            // Queue the webhook event for background processing
            $jobData = [
                'type' => $data['type'] ?? 'unknown',
                'data' => $data['data'] ?? $data,
                'timestamp' => time(),
                'request_id' => $requestId,
                'received_at' => date('c'),
                'raw_payload' => $payload
            ];

            $queued = Queue::push(ProcessFreemiusWebhookJob::class, $jobData, 'webhooks');

            if (!$queued) {
                $this->loggingService->logWebhook('freemius', 'queue_failed', $payload, 500, $requestId);
                ResponseFormatter::serverError('Failed to queue webhook event');
                return;
            }

            // Log successful webhook receipt
            $this->loggingService->logWebhook('freemius', 'queued', $payload, 200, $requestId);

            // Return success response
            ResponseFormatter::success([
                'message' => 'Webhook received and queued for processing',
                'request_id' => $requestId
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logWebhook('freemius', 'exception', $payload ?? '', 500, $requestId, $e->getMessage());
            ResponseFormatter::serverError('Internal server error', $e->getMessage());
        }
    }

    /**
     * Get signature from request headers
     *
     * @return string|null Signature or null if not found
     */
    private function getSignatureFromHeaders(): ?string
    {
        // Check various possible header names for the signature
        $possibleHeaders = [
            'HTTP_X_FREEMIUS_SIGNATURE',
            'HTTP_X_SIGNATURE',
            'HTTP_SIGNATURE'
        ];

        foreach ($possibleHeaders as $header) {
            if (isset($_SERVER[$header])) {
                return $_SERVER[$header];
            }
        }

        // Also check the standard headers array
        $headers = getallheaders();
        if ($headers) {
            $headerNames = [
                'X-Freemius-Signature',
                'X-Signature',
                'Signature'
            ];

            foreach ($headerNames as $headerName) {
                if (isset($headers[$headerName])) {
                    return $headers[$headerName];
                }
            }
        }

        return null;
    }
}
