<?php

namespace Skpassegna\GuardgeoApi\Core;

/**
 * View Class
 *
 * Handles view rendering and template management for the admin interface.
 * Provides methods for rendering views with layouts and passing data.
 *
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class View
{
    /**
     * @var string Base views directory
     */
    private static string $viewsPath;

    /**
     * @var array Global view data
     */
    private static array $globalData = [];

    /**
     * Initialize the view system
     */
    public static function initialize(): void
    {
        self::$viewsPath = ABSPATH . 'src/Views/';
    }

    /**
     * Render a view with layout
     *
     * @param string $view View name (e.g., 'admin/dashboard')
     * @param array $data Data to pass to the view
     * @param string $layout Layout to use (default: 'admin')
     * @return void
     */
    public static function render(string $view, array $data = [], string $layout = 'admin'): void
    {
        // Initialize if not already done
        if (!isset(self::$viewsPath)) {
            self::initialize();
        }

        // Merge global data with view data
        $data = array_merge(self::$globalData, $data);

        // Extract data for use in templates
        extract($data);

        // Start output buffering for content
        ob_start();

        // Include the view file
        $viewFile = self::$viewsPath . $view . '.php';
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new \Exception("View file not found: {$viewFile}");
        }

        // Get the view content
        $content = ob_get_clean();

        // Add content to data for layout
        $data['content'] = $content;
        extract($data);

        // Include the layout file
        $layoutFile = self::$viewsPath . 'layouts/' . $layout . '.php';
        if (file_exists($layoutFile)) {
            include $layoutFile;
        } else {
            throw new \Exception("Layout file not found: {$layoutFile}");
        }
    }

    /**
     * Render a view without layout (partial)
     *
     * @param string $view View name
     * @param array $data Data to pass to the view
     * @return string Rendered content
     */
    public static function partial(string $view, array $data = []): string
    {
        // Initialize if not already done
        if (!isset(self::$viewsPath)) {
            self::initialize();
        }

        // Merge global data with view data
        $data = array_merge(self::$globalData, $data);

        // Extract data for use in template
        extract($data);

        // Start output buffering
        ob_start();

        // Include the view file
        $viewFile = self::$viewsPath . $view . '.php';
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new \Exception("View file not found: {$viewFile}");
        }

        // Return the content
        return ob_get_clean();
    }

    /**
     * Set global data available to all views
     *
     * @param array $data Global data
     * @return void
     */
    public static function setGlobalData(array $data): void
    {
        self::$globalData = array_merge(self::$globalData, $data);
    }

    /**
     * Add a single global variable
     *
     * @param string $key Variable name
     * @param mixed $value Variable value
     * @return void
     */
    public static function addGlobal(string $key, $value): void
    {
        self::$globalData[$key] = $value;
    }

    /**
     * Get global data
     *
     * @return array Global data
     */
    public static function getGlobalData(): array
    {
        return self::$globalData;
    }

    /**
     * Clear global data
     *
     * @return void
     */
    public static function clearGlobalData(): void
    {
        self::$globalData = [];
    }

    /**
     * Check if a view file exists
     *
     * @param string $view View name
     * @return bool True if view exists
     */
    public static function exists(string $view): bool
    {
        if (!isset(self::$viewsPath)) {
            self::initialize();
        }

        $viewFile = self::$viewsPath . $view . '.php';
        return file_exists($viewFile);
    }

    /**
     * Escape HTML output
     *
     * @param string $string String to escape
     * @return string Escaped string
     */
    public static function escape(string $string): string
    {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Format date for display
     *
     * @param string|int $date Date string or timestamp
     * @param string $format Date format (default: 'Y-m-d H:i:s')
     * @return string Formatted date
     */
    public static function formatDate($date, string $format = 'Y-m-d H:i:s'): string
    {
        if (is_numeric($date)) {
            return date($format, $date);
        }
        
        $timestamp = strtotime($date);
        return $timestamp ? date($format, $timestamp) : $date;
    }

    /**
     * Format file size for display
     *
     * @param int $bytes File size in bytes
     * @param int $precision Decimal precision
     * @return string Formatted file size
     */
    public static function formatFileSize(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Generate a URL for admin routes
     *
     * @param string $path Path (e.g., 'logs', 'ip-lookup')
     * @param array $params Query parameters
     * @return string Generated URL
     */
    public static function adminUrl(string $path = '', array $params = []): string
    {
        $url = '/admin';
        
        if ($path) {
            $url .= '/' . ltrim($path, '/');
        }
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }

    /**
     * Generate a URL for API routes
     *
     * @param string $path Path (e.g., 'v1/analyze')
     * @param array $params Query parameters
     * @return string Generated URL
     */
    public static function apiUrl(string $path = '', array $params = []): string
    {
        $url = '/api';
        
        if ($path) {
            $url .= '/' . ltrim($path, '/');
        }
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }

    /**
     * Include CSS file
     *
     * @param string $file CSS file name
     * @return string CSS link tag
     */
    public static function css(string $file): string
    {
        return '<link rel="stylesheet" href="/assets/css/' . $file . '">';
    }

    /**
     * Include JavaScript file
     *
     * @param string $file JS file name
     * @return string Script tag
     */
    public static function js(string $file): string
    {
        return '<script src="/assets/js/' . $file . '"></script>';
    }

    /**
     * Generate CSRF token field
     *
     * @return string CSRF token input field
     */
    public static function csrfField(): string
    {
        // For now, return empty string - CSRF implementation would go here
        return '';
    }

    /**
     * Create pagination links
     *
     * @param int $currentPage Current page number
     * @param int $totalPages Total number of pages
     * @param string $baseUrl Base URL for pagination links
     * @return string Pagination HTML
     */
    public static function pagination(int $currentPage, int $totalPages, string $baseUrl): string
    {
        if ($totalPages <= 1) {
            return '';
        }

        $html = '<div class="pagination">';
        
        // Previous page
        if ($currentPage > 1) {
            $prevUrl = $baseUrl . '?page=' . ($currentPage - 1);
            $html .= '<a href="' . $prevUrl . '" class="pagination-link">&laquo; Previous</a>';
        }
        
        // Page numbers
        $start = max(1, $currentPage - 2);
        $end = min($totalPages, $currentPage + 2);
        
        if ($start > 1) {
            $html .= '<a href="' . $baseUrl . '?page=1" class="pagination-link">1</a>';
            if ($start > 2) {
                $html .= '<span class="pagination-ellipsis">...</span>';
            }
        }
        
        for ($i = $start; $i <= $end; $i++) {
            if ($i == $currentPage) {
                $html .= '<span class="pagination-link active">' . $i . '</span>';
            } else {
                $html .= '<a href="' . $baseUrl . '?page=' . $i . '" class="pagination-link">' . $i . '</a>';
            }
        }
        
        if ($end < $totalPages) {
            if ($end < $totalPages - 1) {
                $html .= '<span class="pagination-ellipsis">...</span>';
            }
            $html .= '<a href="' . $baseUrl . '?page=' . $totalPages . '" class="pagination-link">' . $totalPages . '</a>';
        }
        
        // Next page
        if ($currentPage < $totalPages) {
            $nextUrl = $baseUrl . '?page=' . ($currentPage + 1);
            $html .= '<a href="' . $nextUrl . '" class="pagination-link">Next &raquo;</a>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
}
