<?php

namespace Skpassegna\GuardgeoApi\Core;

use Redis as RedisClient;
use RedisException;
use Skpassegna\GuardgeoApi\Config\Database as DatabaseConfig;

/**
 * Redis Class
 * 
 * Manages Redis connections and provides caching functionality for the GuardGeo API Platform.
 * Implements connection management with fallback handling and cache operations.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class Redis
{
    /**
     * @var RedisClient|null Redis connection instance
     */
    private static ?RedisClient $connection = null;

    /**
     * @var array Redis configuration
     */
    private static array $config = [];

    /**
     * @var bool Connection status
     */
    private static bool $connected = false;

    /**
     * @var bool Fallback mode (when Redis is unavailable)
     */
    private static bool $fallbackMode = false;

    /**
     * @var array In-memory cache for fallback mode
     */
    private static array $fallbackCache = [];

    /**
     * Initialize Redis configuration
     * 
     * @return void
     */
    public static function initialize(): void
    {
        self::$config = DatabaseConfig::getRedisConfig();
    }

    /**
     * Get Redis connection
     * 
     * @return RedisClient|null Redis connection or null if unavailable
     */
    public static function getConnection(): ?RedisClient
    {
        if (self::$connection === null || !self::$connected) {
            self::connect();
        }
        
        return self::$connected ? self::$connection : null;
    }

    /**
     * Establish Redis connection
     * 
     * @return void
     */
    private static function connect(): void
    {
        if (empty(self::$config)) {
            self::initialize();
        }

        try {
            self::$connection = new RedisClient();
            
            $connected = self::$connection->connect(
                self::$config['host'],
                self::$config['port'],
                self::$config['timeout']
            );
            
            if (!$connected) {
                throw new RedisException('Failed to connect to Redis server');
            }
            
            // Authenticate if password is provided
            if (!empty(self::$config['password'])) {
                self::$connection->auth(self::$config['password']);
            }
            
            // Select database
            self::$connection->select(self::$config['database']);
            
            self::$connected = true;
            self::$fallbackMode = false;
            
        } catch (RedisException $e) {
            self::$connected = false;
            self::$fallbackMode = true;
            error_log('Redis connection failed, using fallback mode: ' . $e->getMessage());
        }
    }

    /**
     * Set a cache value
     * 
     * @param string $key Cache key
     * @param mixed $value Cache value
     * @param int $ttl Time to live in seconds (0 = no expiration)
     * @return bool True on success
     */
    public static function set(string $key, $value, int $ttl = 0): bool
    {
        if (self::$fallbackMode) {
            return self::fallbackSet($key, $value, $ttl);
        }

        try {
            $connection = self::getConnection();
            if ($connection === null) {
                return self::fallbackSet($key, $value, $ttl);
            }
            
            $serialized = serialize($value);
            
            if ($ttl > 0) {
                return $connection->setex($key, $ttl, $serialized);
            } else {
                return $connection->set($key, $serialized);
            }
            
        } catch (RedisException $e) {
            error_log('Redis set failed: ' . $e->getMessage());
            return self::fallbackSet($key, $value, $ttl);
        }
    }

    /**
     * Get a cache value
     * 
     * @param string $key Cache key
     * @return mixed Cache value or null if not found
     */
    public static function get(string $key)
    {
        if (self::$fallbackMode) {
            return self::fallbackGet($key);
        }

        try {
            $connection = self::getConnection();
            if ($connection === null) {
                return self::fallbackGet($key);
            }
            
            $value = $connection->get($key);
            
            if ($value === false) {
                return null;
            }
            
            return unserialize($value);
            
        } catch (RedisException $e) {
            error_log('Redis get failed: ' . $e->getMessage());
            return self::fallbackGet($key);
        }
    }

    /**
     * Delete a cache value
     * 
     * @param string $key Cache key
     * @return bool True on success
     */
    public static function delete(string $key): bool
    {
        if (self::$fallbackMode) {
            return self::fallbackDelete($key);
        }

        try {
            $connection = self::getConnection();
            if ($connection === null) {
                return self::fallbackDelete($key);
            }
            
            return $connection->del($key) > 0;
            
        } catch (RedisException $e) {
            error_log('Redis delete failed: ' . $e->getMessage());
            return self::fallbackDelete($key);
        }
    }

    /**
     * Check if a cache key exists
     * 
     * @param string $key Cache key
     * @return bool True if key exists
     */
    public static function exists(string $key): bool
    {
        if (self::$fallbackMode) {
            return self::fallbackExists($key);
        }

        try {
            $connection = self::getConnection();
            if ($connection === null) {
                return self::fallbackExists($key);
            }
            
            return $connection->exists($key) > 0;
            
        } catch (RedisException $e) {
            error_log('Redis exists failed: ' . $e->getMessage());
            return self::fallbackExists($key);
        }
    }

    /**
     * Push value to a list
     * 
     * @param string $key List key
     * @param mixed $value Value to push
     * @return bool True on success
     */
    public static function listPush(string $key, $value): bool
    {
        if (self::$fallbackMode) {
            return self::fallbackListPush($key, $value);
        }

        try {
            $connection = self::getConnection();
            if ($connection === null) {
                return self::fallbackListPush($key, $value);
            }
            
            return $connection->rPush($key, serialize($value)) > 0;
            
        } catch (RedisException $e) {
            error_log('Redis list push failed: ' . $e->getMessage());
            return self::fallbackListPush($key, $value);
        }
    }

    /**
     * Pop value from a list
     * 
     * @param string $key List key
     * @return mixed Popped value or null if list is empty
     */
    public static function listPop(string $key)
    {
        if (self::$fallbackMode) {
            return self::fallbackListPop($key);
        }

        try {
            $connection = self::getConnection();
            if ($connection === null) {
                return self::fallbackListPop($key);
            }
            
            $value = $connection->lPop($key);
            
            if ($value === false) {
                return null;
            }
            
            return unserialize($value);
            
        } catch (RedisException $e) {
            error_log('Redis list pop failed: ' . $e->getMessage());
            return self::fallbackListPop($key);
        }
    }

    /**
     * Test Redis connection
     * 
     * @return bool True if connection is working
     */
    public static function testConnection(): bool
    {
        try {
            $connection = self::getConnection();
            if ($connection === null) {
                return false;
            }
            
            return $connection->ping() === '+PONG';
            
        } catch (RedisException $e) {
            return false;
        }
    }

    /**
     * Check if Redis is available
     * 
     * @return bool True if Redis is available
     */
    public static function isAvailable(): bool
    {
        return self::$connected && !self::$fallbackMode;
    }

    /**
     * Close Redis connection
     * 
     * @return void
     */
    public static function close(): void
    {
        if (self::$connection !== null) {
            try {
                self::$connection->close();
            } catch (RedisException $e) {
                // Ignore close errors
            }
        }
        
        self::$connection = null;
        self::$connected = false;
    }

    // Fallback methods for when Redis is unavailable

    private static function fallbackSet(string $key, $value, int $ttl): bool
    {
        $expiry = $ttl > 0 ? time() + $ttl : 0;
        self::$fallbackCache[$key] = ['value' => $value, 'expiry' => $expiry];
        return true;
    }

    private static function fallbackGet(string $key)
    {
        if (!isset(self::$fallbackCache[$key])) {
            return null;
        }
        
        $item = self::$fallbackCache[$key];
        
        if ($item['expiry'] > 0 && time() > $item['expiry']) {
            unset(self::$fallbackCache[$key]);
            return null;
        }
        
        return $item['value'];
    }

    private static function fallbackDelete(string $key): bool
    {
        unset(self::$fallbackCache[$key]);
        return true;
    }

    private static function fallbackExists(string $key): bool
    {
        if (!isset(self::$fallbackCache[$key])) {
            return false;
        }
        
        $item = self::$fallbackCache[$key];
        
        if ($item['expiry'] > 0 && time() > $item['expiry']) {
            unset(self::$fallbackCache[$key]);
            return false;
        }
        
        return true;
    }

    private static function fallbackListPush(string $key, $value): bool
    {
        if (!isset(self::$fallbackCache[$key])) {
            self::$fallbackCache[$key] = ['value' => [], 'expiry' => 0];
        }
        
        self::$fallbackCache[$key]['value'][] = $value;
        return true;
    }

    private static function fallbackListPop(string $key)
    {
        if (!isset(self::$fallbackCache[$key]) || empty(self::$fallbackCache[$key]['value'])) {
            return null;
        }
        
        return array_shift(self::$fallbackCache[$key]['value']);
    }
}
