<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Core\ResponseFormatter;
use Skpassegna\GuardgeoApi\Core\Database;
use Skpassegna\GuardgeoApi\Core\Redis;
use Skpassegna\GuardgeoApi\Services\FreemiusService;
use Skpassegna\GuardgeoApi\Services\IpRegistryService;
use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * ApiController Class
 *
 * Handles REST API endpoints for the GuardGeo WordPress plugin.
 * Provides IP analysis and health check functionality.
 *
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class ApiController
{
    /**
     * @var FreemiusService Freemius service instance
     */
    private FreemiusService $freemiusService;

    /**
     * @var IpRegistryService IP registry service instance
     */
    private IpRegistryService $ipRegistryService;

    /**
     * @var LoggingService Logging service instance
     */
    private LoggingService $loggingService;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->freemiusService = new FreemiusService();
        $this->ipRegistryService = new IpRegistryService();
        $this->loggingService = new LoggingService();
    }

    /**
     * Analyze IP address endpoint
     *
     * POST /api/v1/analyze
     *
     * Expected payload:
     * {
     *   "ip": "***********",
     *   "visitor_hash": "uuid-v4-string",
     *   "plugin_id": 12345,
     *   "install_id": 67890,
     *   "url": "https://example.com"
     * }
     *
     * @return void
     */
    public function analyze(): void
    {
        $requestId = ResponseFormatter::generateRequestId();
        $startTime = microtime(true);
        $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        try {
            // Validate request method
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->logAndRespond('analyze', $clientIp, null, 405, null,
                    microtime(true) - $startTime, $requestId);
                ResponseFormatter::error('Method not allowed', 405, 'METHOD_NOT_ALLOWED', null, $requestId);
                return;
            }

            // Get and validate JSON payload
            $rawPayload = file_get_contents('php://input');

            if (empty($rawPayload)) {
                $this->logAndRespond('analyze', $clientIp, null, 400, null,
                    microtime(true) - $startTime, $requestId);
                ResponseFormatter::error('Empty request payload', 400, 'EMPTY_PAYLOAD', null, $requestId);
                return;
            }

            $requestData = json_decode($rawPayload, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logAndRespond('analyze', $clientIp, ['raw_payload' => $rawPayload], 400, null,
                    microtime(true) - $startTime, $requestId);
                ResponseFormatter::error('Invalid JSON payload', 400, 'INVALID_JSON', null, $requestId);
                return;
            }

            // Validate required parameters
            $validationResult = $this->validateAnalyzeRequest($requestData);

            if (!$validationResult['valid']) {
                $this->logAndRespond('analyze', $clientIp, $requestData, 422, $validationResult['errors'],
                    microtime(true) - $startTime, $requestId);
                ResponseFormatter::validationError($validationResult['errors'], 'Request validation failed');
                return;
            }

            // Extract validated parameters
            $ip = $requestData['ip'];
            $visitorHash = $requestData['visitor_hash'];
            $pluginId = (int)$requestData['plugin_id'];
            $installId = (int)$requestData['install_id'];
            $url = $requestData['url'] ?? null;

            // Validate Freemius installation
            $freemiusValidation = $this->freemiusService->validateInstallation($pluginId, $installId, $url);

            if (!$freemiusValidation['valid']) {
                $this->logAndRespond('analyze', $clientIp, $requestData, 403, $freemiusValidation,
                    microtime(true) - $startTime, $requestId);
                ResponseFormatter::authorizationError($freemiusValidation['reason']);
                return;
            }

            // Get IP intelligence data
            $ipData = $this->ipRegistryService->getIpData($ip);

            if (isset($ipData['error'])) {
                $this->logAndRespond('analyze', $clientIp, $requestData, 500, $ipData,
                    microtime(true) - $startTime, $requestId);
                ResponseFormatter::serverError('Failed to analyze IP address', $ipData['message']);
                return;
            }

            // Prepare response data
            $responseData = [
                'ip' => $ip,
                'visitor_hash' => $visitorHash,
                'analysis' => $ipData,
                'installation' => [
                    'plugin_id' => $pluginId,
                    'install_id' => $installId,
                    'is_premium' => $freemiusValidation['is_premium'],
                    'has_active_license' => $freemiusValidation['has_active_license']
                ],
                'request_id' => $requestId
            ];

            // Log successful request
            $this->logAndRespond('analyze', $clientIp, $requestData, 200, $responseData,
                microtime(true) - $startTime, $requestId);

            // Return success response
            ResponseFormatter::success($responseData);

        } catch (\Exception $e) {
            $this->loggingService->logException('API analyze error', $e, [
                'request_id' => $requestId,
                'client_ip' => $clientIp
            ]);

            $this->logAndRespond('analyze', $clientIp, $requestData ?? null, 500, null,
                microtime(true) - $startTime, $requestId);

            ResponseFormatter::serverError('Internal server error',
                defined('DEBUG') && DEBUG ? $e->getMessage() : null);
        }
    }

    /**
     * Health check endpoint
     *
     * GET /api/v1/health
     *
     * @return void
     */
    public function health(): void
    {
        $health = [
            'status' => 'ok',
            'timestamp' => date('c'),
            'version' => '1.0.0',
            'services' => [
                'api' => 'operational',
                'database' => $this->checkDatabaseHealth(),
                'redis' => $this->checkRedisHealth(),
                'freemius' => 'operational',
                'ipregistry' => 'operational'
            ]
        ];

        ResponseFormatter::success($health);
    }

    /**
     * Validate analyze request parameters
     *
     * @param array $data Request data
     * @return array Validation result
     */
    private function validateAnalyzeRequest(array $data): array
    {
        $errors = [];

        // Required fields
        $requiredFields = ['ip', 'visitor_hash', 'plugin_id', 'install_id'];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[$field] = "Field '{$field}' is required";
            }
        }

        // Validate IP address format
        if (isset($data['ip']) && !filter_var($data['ip'], FILTER_VALIDATE_IP)) {
            $errors['ip'] = 'Invalid IP address format';
        }

        // Validate visitor hash format (should be UUID-like)
        if (isset($data['visitor_hash']) && !$this->isValidUuid($data['visitor_hash'])) {
            $errors['visitor_hash'] = 'Invalid visitor hash format (should be UUID)';
        }

        // Validate plugin_id (should be positive integer)
        if (isset($data['plugin_id']) && (!is_numeric($data['plugin_id']) || (int)$data['plugin_id'] <= 0)) {
            $errors['plugin_id'] = 'Plugin ID must be a positive integer';
        }

        // Validate install_id (should be positive integer)
        if (isset($data['install_id']) && (!is_numeric($data['install_id']) || (int)$data['install_id'] <= 0)) {
            $errors['install_id'] = 'Install ID must be a positive integer';
        }

        // Validate URL format if provided
        if (isset($data['url']) && !empty($data['url']) && !filter_var($data['url'], FILTER_VALIDATE_URL)) {
            $errors['url'] = 'Invalid URL format';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Check if string is a valid UUID format
     *
     * @param string $uuid UUID string
     * @return bool True if valid UUID format
     */
    private function isValidUuid(string $uuid): bool
    {
        return preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $uuid) === 1;
    }

    /**
     * Log API request and response
     *
     * @param string $endpoint Endpoint name
     * @param string $clientIp Client IP address
     * @param array|null $requestData Request data
     * @param int $responseStatus Response status code
     * @param array|null $responseData Response data
     * @param float $processingTime Processing time in seconds
     * @param string $requestId Request ID
     * @return void
     */
    private function logAndRespond(
        string $endpoint,
        string $clientIp,
        ?array $requestData,
        int $responseStatus,
        ?array $responseData,
        float $processingTime,
        string $requestId
    ): void {
        $this->loggingService->logApiRequest(
            'POST',
            "/api/v1/{$endpoint}",
            $clientIp,
            $requestData,
            $responseStatus,
            $responseData,
            (int)($processingTime * 1000), // Convert to milliseconds
            $requestId
        );
    }

    /**
     * Check database health
     *
     * @return string Database status
     */
    private function checkDatabaseHealth(): string
    {
        try {
            return Database::testConnection() ? 'operational' : 'error';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * Check Redis health
     *
     * @return string Redis status
     */
    private function checkRedisHealth(): string
    {
        try {
            return Redis::testConnection() ? 'operational' : 'fallback';
        } catch (\Exception $e) {
            return 'fallback';
        }
    }
}
