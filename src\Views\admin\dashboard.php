<?php
/**
 * Admin Dashboard View
 * 
 * Displays API usage statistics, system metrics, and administrative overview.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Extract variables
$stats = $stats ?? [];
$recentLogs = $recentLogs ?? [];
$systemInfo = $systemInfo ?? [];
?>

<div class="page-header">
    <h1 class="page-title">Dashboard</h1>
    <p class="page-description">System overview and API usage statistics</p>
</div>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
            <h3 class="stat-number"><?= number_format($stats['total_requests'] ?? 0) ?></h3>
            <p class="stat-label">Total API Requests</p>
            <span class="stat-change positive">+<?= number_format($stats['requests_today'] ?? 0) ?> today</span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
            <h3 class="stat-number"><?= number_format($stats['successful_requests'] ?? 0) ?></h3>
            <p class="stat-label">Successful Requests</p>
            <span class="stat-change positive"><?= number_format($stats['success_rate'] ?? 0, 1) ?>% success rate</span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">❌</div>
        <div class="stat-content">
            <h3 class="stat-number"><?= number_format($stats['failed_requests'] ?? 0) ?></h3>
            <p class="stat-label">Failed Requests</p>
            <span class="stat-change negative"><?= number_format($stats['failures_today'] ?? 0) ?> today</span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">🌍</div>
        <div class="stat-content">
            <h3 class="stat-number"><?= number_format($stats['unique_ips'] ?? 0) ?></h3>
            <p class="stat-label">Unique IP Addresses</p>
            <span class="stat-change neutral"><?= number_format($stats['new_ips_today'] ?? 0) ?> new today</span>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="charts-section">
    <div class="chart-container">
        <h3>API Requests (Last 7 Days)</h3>
        <div class="chart-placeholder" id="requests-chart">
            <canvas id="requestsChart" width="400" height="200"></canvas>
        </div>
    </div>

    <div class="chart-container">
        <h3>Response Status Distribution</h3>
        <div class="chart-placeholder" id="status-chart">
            <canvas id="statusChart" width="400" height="200"></canvas>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="recent-activity">
    <h3>Recent API Activity</h3>
    <div class="activity-table">
        <?php if (!empty($recentLogs)): ?>
        <table class="table">
            <thead>
                <tr>
                    <th>Time</th>
                    <th>IP Address</th>
                    <th>Endpoint</th>
                    <th>Status</th>
                    <th>Response Time</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($recentLogs as $log): ?>
                <tr>
                    <td><?= htmlspecialchars(date('H:i:s', strtotime($log['created_at']))) ?></td>
                    <td>
                        <code><?= htmlspecialchars($log['ip_address']) ?></code>
                    </td>
                    <td>
                        <span class="endpoint"><?= htmlspecialchars($log['method']) ?> <?= htmlspecialchars($log['endpoint']) ?></span>
                    </td>
                    <td>
                        <span class="status status-<?= $log['response_status'] >= 200 && $log['response_status'] < 300 ? 'success' : ($log['response_status'] >= 400 ? 'error' : 'warning') ?>">
                            <?= $log['response_status'] ?>
                        </span>
                    </td>
                    <td><?= $log['processing_time'] ?>ms</td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <div class="empty-state">
            <p>No recent API activity to display.</p>
        </div>
        <?php endif; ?>
    </div>
    <div class="activity-footer">
        <a href="/admin/logs" class="btn btn-secondary">View All Logs</a>
    </div>
</div>

<!-- System Information -->
<div class="system-info">
    <h3>System Information</h3>
    <div class="info-grid">
        <div class="info-item">
            <strong>Database Status:</strong>
            <span class="status status-<?= ($systemInfo['database_connected'] ?? false) ? 'success' : 'error' ?>">
                <?= ($systemInfo['database_connected'] ?? false) ? 'Connected' : 'Disconnected' ?>
            </span>
        </div>
        <div class="info-item">
            <strong>Redis Status:</strong>
            <span class="status status-<?= ($systemInfo['redis_connected'] ?? false) ? 'success' : 'error' ?>">
                <?= ($systemInfo['redis_connected'] ?? false) ? 'Connected' : 'Disconnected' ?>
            </span>
        </div>
        <div class="info-item">
            <strong>Cached Installations:</strong>
            <span><?= number_format($systemInfo['cached_installations'] ?? 0) ?></span>
        </div>
        <div class="info-item">
            <strong>Cached IP Records:</strong>
            <span><?= number_format($systemInfo['cached_ips'] ?? 0) ?></span>
        </div>
        <div class="info-item">
            <strong>Last Webhook:</strong>
            <span><?= $systemInfo['last_webhook'] ? htmlspecialchars(date('Y-m-d H:i:s', strtotime($systemInfo['last_webhook']))) : 'Never' ?></span>
        </div>
        <div class="info-item">
            <strong>System Uptime:</strong>
            <span><?= $systemInfo['uptime'] ?? 'Unknown' ?></span>
        </div>
    </div>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin: 0;
}

.stat-label {
    color: #7f8c8d;
    margin: 0.25rem 0;
    font-size: 0.9rem;
}

.stat-change {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.stat-change.positive {
    background: #d4edda;
    color: #155724;
}

.stat-change.negative {
    background: #f8d7da;
    color: #721c24;
}

.stat-change.neutral {
    background: #e2e3e5;
    color: #383d41;
}

.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-container h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.chart-placeholder {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 4px;
}

.recent-activity {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.recent-activity h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.activity-table {
    margin-bottom: 1rem;
}

.endpoint {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-success {
    background: #d4edda;
    color: #155724;
}

.status-error {
    background: #f8d7da;
    color: #721c24;
}

.status-warning {
    background: #fff3cd;
    color: #856404;
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: #7f8c8d;
}

.activity-footer {
    text-align: right;
}

.system-info {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.system-info h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 4px;
}

@media (max-width: 768px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Simple chart implementation using Canvas
document.addEventListener('DOMContentLoaded', function() {
    // Sample data - in real implementation, this would come from the server
    const requestsData = <?= json_encode($stats['daily_requests'] ?? [0,0,0,0,0,0,0]) ?>;
    const statusData = <?= json_encode($stats['status_distribution'] ?? ['200' => 80, '400' => 15, '500' => 5]) ?>;
    
    // Draw requests chart
    drawLineChart('requestsChart', requestsData, 'Requests');
    
    // Draw status chart
    drawPieChart('statusChart', statusData);
});

function drawLineChart(canvasId, data, label) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    const padding = 40;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Find max value for scaling
    const maxValue = Math.max(...data, 1);
    
    // Draw axes
    ctx.strokeStyle = '#ddd';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();
    
    // Draw data line
    ctx.strokeStyle = '#3498db';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    for (let i = 0; i < data.length; i++) {
        const x = padding + (i * (width - 2 * padding)) / (data.length - 1);
        const y = height - padding - (data[i] * (height - 2 * padding)) / maxValue;
        
        if (i === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }
    ctx.stroke();
    
    // Draw data points
    ctx.fillStyle = '#3498db';
    for (let i = 0; i < data.length; i++) {
        const x = padding + (i * (width - 2 * padding)) / (data.length - 1);
        const y = height - padding - (data[i] * (height - 2 * padding)) / maxValue;
        
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, 2 * Math.PI);
        ctx.fill();
    }
}

function drawPieChart(canvasId, data) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 20;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const total = Object.values(data).reduce((sum, value) => sum + value, 0);
    const colors = ['#27ae60', '#f39c12', '#e74c3c'];
    
    let currentAngle = -Math.PI / 2;
    let colorIndex = 0;
    
    for (const [status, value] of Object.entries(data)) {
        const sliceAngle = (value / total) * 2 * Math.PI;
        
        // Draw slice
        ctx.fillStyle = colors[colorIndex % colors.length];
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
        ctx.closePath();
        ctx.fill();
        
        currentAngle += sliceAngle;
        colorIndex++;
    }
}
</script>
