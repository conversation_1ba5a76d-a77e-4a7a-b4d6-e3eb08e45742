<?php

namespace Skpassegna\GuardgeoApi\Core;

/**
 * Router Class
 * 
 * Handles URL routing and request dispatching for the GuardGeo API Platform.
 * Implements pattern matching for dynamic routes and parameter extraction.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class Router
{
    /**
     * @var array Registered routes
     */
    private array $routes = [];

    /**
     * Add a route to the router
     * 
     * @param string $method HTTP method (GET, POST, PUT, DELETE)
     * @param string $pattern URL pattern (can include parameters like {id})
     * @param string $controller Controller class name
     * @param string $action Controller method name
     * @return void
     */
    public function addRoute(string $method, string $pattern, string $controller, string $action): void
    {
        $this->routes[] = [
            'method' => strtoupper($method),
            'pattern' => $pattern,
            'controller' => $controller,
            'action' => $action
        ];
    }

    /**
     * Route a request to the appropriate controller and action
     * 
     * @param string $method HTTP method
     * @param string $uri Request URI
     * @return array|null Route information or null if not found
     */
    public function route(string $method, string $uri): ?array
    {
        $method = strtoupper($method);
        
        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }
            
            $params = $this->matchRoute($route['pattern'], $uri);
            
            if ($params !== false) {
                return [
                    'controller' => $route['controller'],
                    'action' => $route['action'],
                    'params' => $params
                ];
            }
        }
        
        return null;
    }

    /**
     * Match a route pattern against a URI
     * 
     * @param string $pattern Route pattern
     * @param string $uri Request URI
     * @return array|false Parameters array or false if no match
     */
    private function matchRoute(string $pattern, string $uri): array|false
    {
        // Escape special regex characters except for parameter placeholders
        $pattern = preg_quote($pattern, '/');
        
        // Replace parameter placeholders with regex patterns
        $pattern = preg_replace('/\\\{([a-zA-Z_][a-zA-Z0-9_]*)\\\}/', '(?P<$1>[^/]+)', $pattern);
        
        // Add start and end anchors
        $pattern = '/^' . $pattern . '$/';
        
        if (preg_match($pattern, $uri, $matches)) {
            // Extract named parameters
            $params = [];
            foreach ($matches as $key => $value) {
                if (is_string($key)) {
                    $params[$key] = $value;
                }
            }
            return $params;
        }
        
        return false;
    }

    /**
     * Get all registered routes
     * 
     * @return array All routes
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }

    /**
     * Generate URL for a named route
     * 
     * @param string $controller Controller class
     * @param string $action Action method
     * @param array $params Parameters to substitute
     * @return string|null Generated URL or null if route not found
     */
    public function generateUrl(string $controller, string $action, array $params = []): ?string
    {
        foreach ($this->routes as $route) {
            if ($route['controller'] === $controller && $route['action'] === $action) {
                $url = $route['pattern'];
                
                // Replace parameters in the pattern
                foreach ($params as $key => $value) {
                    $url = str_replace('{' . $key . '}', $value, $url);
                }
                
                return $url;
            }
        }
        
        return null;
    }
}
