# GuardGeo API Platform - Apache Configuration
# This file configures URL rewriting and security settings for shared hosting

# Enable URL rewriting
RewriteEngine On

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'"

    # Remove server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(env|log|sql|md|json|lock|yml|yaml)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to sensitive directories
RedirectMatch 404 /vendor/.*$
RedirectMatch 404 /src/.*$
RedirectMatch 404 /database/.*$
RedirectMatch 404 /logs/.*$
RedirectMatch 404 /tests/.*$
RedirectMatch 404 /docs/.*$
RedirectMatch 404 /\..*$

# Prevent access to hidden files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block common attack patterns
<IfModule mod_rewrite.c>
    # Block SQL injection attempts
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC|EXECUTE|SCRIPT) [NC]
    RewriteRule ^(.*)$ - [F,L]

    # Block suspicious user agents
    RewriteCond %{HTTP_USER_AGENT} (libwww-perl|wget|python|nikto|curl|scan|java|winhttp|clshttp|loader) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (%0A|%0D|%27|%3C|%3E|%00) [NC]
    RewriteRule ^(.*)$ - [F,L]

    # Block suspicious request methods
    RewriteCond %{REQUEST_METHOD} ^(HEAD|TRACE|DELETE|TRACK|DEBUG) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# Set proper MIME types
AddType application/json .json
AddType text/plain .log

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Main rewrite rules - redirect all requests to index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Handle CORS for API requests
<IfModule mod_headers.c>
    # Handle preflight requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
    
    # Set CORS headers for API requests
    SetEnvIf Request_URI "^/api/" IS_API
    Header always set Access-Control-Allow-Origin "*" env=IS_API
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" env=IS_API
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" env=IS_API
    Header always set Access-Control-Max-Age "3600" env=IS_API
</IfModule>

# PHP settings for shared hosting
<IfModule mod_php.c>
    php_value memory_limit 256M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value post_max_size 10M
    php_value upload_max_filesize 10M
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log ../logs/error.log
</IfModule>
