---
type: "always_apply"
---

# Requirements Document

## Introduction

The GuardGeo API Platform is a private proprietary backend system designed to serve the GuardGeo WordPress plugin with IP intelligence and fraud prevention capabilities. The platform validates plugin requests via Freemius integration, fetches IP intelligence data from ipRegistry, and provides comprehensive admin management tools. The system must be built using pure PHP with MVC architecture following SOLID principles, deployable on shared hosting via FTP with a single SQL file for database setup.

## Requirements

### Requirement 1

**User Story:** As a GuardGeo WordPress plugin, I want to analyze visitor IP addresses through a secure API, so that I can make informed security decisions about website visitors.

#### Acceptance Criteria

1. WHEN a plugin sends a POST request to /api/v1/analyze with valid parameters THEN the system SHALL validate the request against Freemius API
2. WHEN the Freemius validation succeeds THEN the system SHALL check for cached IP data less than 3 days old
3. IF cached IP data exists and is fresh THEN the system SHALL return the cached data immediately
4. IF cached IP data is missing or stale THEN the system SHALL fetch fresh data from ipRegistry API
5. WHEN fresh IP data is retrieved THEN the system SHALL store it in PostgreSQL with full ipRegistry payload
6. WHEN any API request is processed THEN the system SHALL log the complete request details including payload, status, and timestamps

### Requirement 2

**User Story:** As a system administrator, I want Freemius integration to validate plugin installations, so that only authorized users can access the API services.

#### Acceptance Criteria

1. WHEN an API request is received THEN the system SHALL validate that plugin_id exists as an active product in Freemius
2. WHEN validating installation THEN the system SHALL confirm install_id belongs to the given plugin_id
3. WHEN checking subscription status THEN the system SHALL verify the installation has an active paid subscription/license
4. WHEN checking installation status THEN the system SHALL ensure the installation is not uninstalled or inactive
5. IF any Freemius validation fails THEN the system SHALL return 403 Forbidden response
6. WHEN Freemius data is retrieved THEN the system SHALL cache it in Redis with appropriate expiration
7. WHEN webhook updates are received THEN the system SHALL invalidate affected cache entries

### Requirement 3

**User Story:** As a system administrator, I want to receive and process Freemius webhooks, so that the system maintains up-to-date installation and product data.

#### Acceptance Criteria

1. WHEN a webhook is received at /webhooks/freemius THEN the system SHALL validate the HMAC signature
2. IF HMAC validation fails THEN the system SHALL return 401 Unauthorized response
3. WHEN HMAC validation succeeds THEN the system SHALL push the event to Redis Queue
4. WHEN an event is queued THEN a background worker SHALL process the event asynchronously
5. WHEN processing webhook events THEN the worker SHALL update relevant Freemius data in PostgreSQL
6. WHEN updating Freemius data THEN the worker SHALL invalidate affected Redis cache entries
7. WHEN webhook processing completes THEN the system SHALL log the processing results

### Requirement 4

**User Story:** As an admin user, I want to access a web interface to manage the system, so that I can monitor API usage, view logs, and perform administrative tasks.

#### Acceptance Criteria

1. WHEN accessing admin routes THEN the system SHALL require email and password authentication
2. WHEN authenticating users THEN the system SHALL restrict email domains to configured allowed domains only
3. WHEN password is provided THEN the system SHALL require minimum 12 characters
4. WHEN displaying dashboard THEN the system SHALL show API usage statistics including requests, successes, and failures
5. WHEN viewing logs THEN the system SHALL provide search functionality for all API request logs
6. WHEN searching IP data THEN the system SHALL allow manual lookup and addition via ipRegistry
7. WHEN triggering refresh tasks THEN the system SHALL allow manual updates of stored IPs and Freemius data sync

### Requirement 5

**User Story:** As a system architect, I want the platform built with MVC architecture and SOLID principles, so that the codebase is maintainable, testable, and follows best practices.

#### Acceptance Criteria

1. WHEN structuring the application THEN the system SHALL follow MVC pattern with clear separation of concerns
2. WHEN implementing classes THEN the system SHALL adhere to SOLID principles (Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion)
3. WHEN routing requests THEN the system SHALL use a centralized Router class to direct requests to appropriate controllers
4. WHEN handling responses THEN the system SHALL use a ResponseFormatter for consistent JSON API responses
5. WHEN accessing data THEN the system SHALL use Model classes for database and Redis operations
6. WHEN implementing business logic THEN the system SHALL use Service classes to encapsulate complex operations
7. WHEN managing dependencies THEN the system SHALL use dependency injection where appropriate

### Requirement 6

**User Story:** As a deployment engineer, I want the system deployable on shared hosting, so that it can be easily installed via FTP without complex server configurations.

#### Acceptance Criteria

1. WHEN deploying the application THEN the system SHALL work on shared hosting with Apache web server
2. WHEN setting up the database THEN the system SHALL provide a single SQL file that creates all necessary tables
3. WHEN configuring the application THEN the system SHALL require no environment-specific config files
4. WHEN enabling debugging THEN the system SHALL provide a simple toggle option without separate environments
5. WHEN managing dependencies THEN the system SHALL use only approved PHP libraries: GuzzleHttp, monolog/monolog, sentry/sentry, phpunit/phpunit
6. WHEN serving requests THEN the system SHALL route all requests through public/index.php as the single entry point
7. WHEN providing containerization THEN the system SHALL include a simple docker-compose.yml file for development

### Requirement 7

**User Story:** As a security administrator, I want comprehensive logging and monitoring, so that I can track all system activities and identify potential issues.

#### Acceptance Criteria

1. WHEN any API request is made THEN the system SHALL log the complete request including IP, parameters, validation results, and response
2. WHEN webhook events are processed THEN the system SHALL log the event type, processing status, and any errors
3. WHEN admin actions are performed THEN the system SHALL log the user, action, and timestamp
4. WHEN errors occur THEN the system SHALL log detailed error information including stack traces
5. WHEN logging events THEN the system SHALL use structured logging format for easy parsing
6. WHEN storing logs THEN the system SHALL rotate logs to prevent disk space issues
7. WHEN critical errors occur THEN the system SHALL optionally send alerts via Sentry integration

### Requirement 8

**User Story:** As a system administrator, I want role-based access control for admin users, so that different team members have appropriate permissions.

#### Acceptance Criteria

1. WHEN creating the initial setup THEN the system SHALL have only Super Admin account inserted manually via SQL
2. WHEN Super Admin is authenticated THEN they SHALL have full access to create and manage all other accounts
3. WHEN creating Dev accounts THEN they SHALL have access to technical admin tools and code-related features
4. WHEN creating Marketing accounts THEN they SHALL have access to marketing-related analytics and tools
5. WHEN creating Sales accounts THEN they SHALL have access to sales-related analytics and data
6. WHEN any non-Super Admin user attempts restricted actions THEN the system SHALL deny access with appropriate error message
7. WHEN user roles are assigned THEN the system SHALL enforce permissions consistently across all admin interfaces