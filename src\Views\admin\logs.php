<?php
/**
 * Admin Logs View
 * 
 * Displays API request logs with search, filtering, and pagination.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Extract variables
$logs = $logs ?? [];
$pagination = $pagination ?? [];
$filters = $filters ?? [];
$totalLogs = $totalLogs ?? 0;
$currentPage = $currentPage ?? 1;
$totalPages = $totalPages ?? 1;
?>

<div class="page-header">
    <h1 class="page-title">API Request Logs</h1>
    <p class="page-description">View and search through all API request logs</p>
</div>

<!-- Search and Filter Section -->
<div class="filters-section">
    <form method="GET" action="/admin/logs" class="filters-form" id="filtersForm">
        <div class="filter-row">
            <div class="filter-group">
                <label for="search" class="filter-label">Search</label>
                <input 
                    type="text" 
                    id="search" 
                    name="search" 
                    class="form-control" 
                    placeholder="IP address, endpoint, or response data..."
                    value="<?= htmlspecialchars($filters['search'] ?? '') ?>"
                >
            </div>

            <div class="filter-group">
                <label for="status" class="filter-label">Status Code</label>
                <select id="status" name="status" class="form-control">
                    <option value="">All Status Codes</option>
                    <option value="200" <?= ($filters['status'] ?? '') === '200' ? 'selected' : '' ?>>200 - Success</option>
                    <option value="400" <?= ($filters['status'] ?? '') === '400' ? 'selected' : '' ?>>400 - Bad Request</option>
                    <option value="401" <?= ($filters['status'] ?? '') === '401' ? 'selected' : '' ?>>401 - Unauthorized</option>
                    <option value="403" <?= ($filters['status'] ?? '') === '403' ? 'selected' : '' ?>>403 - Forbidden</option>
                    <option value="404" <?= ($filters['status'] ?? '') === '404' ? 'selected' : '' ?>>404 - Not Found</option>
                    <option value="500" <?= ($filters['status'] ?? '') === '500' ? 'selected' : '' ?>>500 - Server Error</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="method" class="filter-label">HTTP Method</label>
                <select id="method" name="method" class="form-control">
                    <option value="">All Methods</option>
                    <option value="GET" <?= ($filters['method'] ?? '') === 'GET' ? 'selected' : '' ?>>GET</option>
                    <option value="POST" <?= ($filters['method'] ?? '') === 'POST' ? 'selected' : '' ?>>POST</option>
                    <option value="PUT" <?= ($filters['method'] ?? '') === 'PUT' ? 'selected' : '' ?>>PUT</option>
                    <option value="DELETE" <?= ($filters['method'] ?? '') === 'DELETE' ? 'selected' : '' ?>>DELETE</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="date_from" class="filter-label">Date From</label>
                <input 
                    type="date" 
                    id="date_from" 
                    name="date_from" 
                    class="form-control"
                    value="<?= htmlspecialchars($filters['date_from'] ?? '') ?>"
                >
            </div>

            <div class="filter-group">
                <label for="date_to" class="filter-label">Date To</label>
                <input 
                    type="date" 
                    id="date_to" 
                    name="date_to" 
                    class="form-control"
                    value="<?= htmlspecialchars($filters['date_to'] ?? '') ?>"
                >
            </div>
        </div>

        <div class="filter-actions">
            <button type="submit" class="btn btn-primary">Apply Filters</button>
            <a href="/admin/logs" class="btn btn-secondary">Clear Filters</a>
            <button type="button" class="btn btn-success" id="exportBtn">Export CSV</button>
        </div>
    </form>
</div>

<!-- Results Summary -->
<div class="results-summary">
    <p>
        Showing <?= number_format(count($logs)) ?> of <?= number_format($totalLogs) ?> log entries
        <?php if (!empty($filters['search'])): ?>
            for search: <strong>"<?= htmlspecialchars($filters['search']) ?>"</strong>
        <?php endif; ?>
    </p>
</div>

<!-- Logs Table -->
<div class="logs-table-container">
    <?php if (!empty($logs)): ?>
    <table class="table logs-table">
        <thead>
            <tr>
                <th>Timestamp</th>
                <th>IP Address</th>
                <th>Method</th>
                <th>Endpoint</th>
                <th>Status</th>
                <th>Response Time</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($logs as $log): ?>
            <tr class="log-row" data-log-id="<?= $log['id'] ?>">
                <td class="timestamp">
                    <div class="datetime">
                        <?= date('Y-m-d', strtotime($log['created_at'])) ?>
                    </div>
                    <div class="time">
                        <?= date('H:i:s', strtotime($log['created_at'])) ?>
                    </div>
                </td>
                <td class="ip-address">
                    <code><?= htmlspecialchars($log['ip_address']) ?></code>
                </td>
                <td class="method">
                    <span class="method-badge method-<?= strtolower($log['method']) ?>">
                        <?= htmlspecialchars($log['method']) ?>
                    </span>
                </td>
                <td class="endpoint">
                    <code><?= htmlspecialchars($log['endpoint']) ?></code>
                </td>
                <td class="status">
                    <span class="status-badge status-<?= $log['response_status'] >= 200 && $log['response_status'] < 300 ? 'success' : ($log['response_status'] >= 400 ? 'error' : 'warning') ?>">
                        <?= $log['response_status'] ?>
                    </span>
                </td>
                <td class="response-time">
                    <?= $log['processing_time'] ?>ms
                </td>
                <td class="actions">
                    <button class="btn-small btn-info" onclick="viewLogDetails(<?= $log['id'] ?>)">
                        View Details
                    </button>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php else: ?>
    <div class="empty-state">
        <div class="empty-icon">📋</div>
        <h3>No logs found</h3>
        <p>No API request logs match your current filters.</p>
        <?php if (!empty($filters)): ?>
        <a href="/admin/logs" class="btn btn-secondary">Clear Filters</a>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($totalPages > 1): ?>
<div class="pagination-container">
    <?= \Skpassegna\GuardgeoApi\Core\View::pagination($currentPage, $totalPages, '/admin/logs') ?>
</div>
<?php endif; ?>

<!-- Log Details Modal -->
<div id="logModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Log Details</h3>
            <button class="modal-close" onclick="closeLogModal()">&times;</button>
        </div>
        <div class="modal-body" id="logModalBody">
            <!-- Log details will be loaded here -->
        </div>
    </div>
</div>

<style>
.filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.filter-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.filter-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.results-summary {
    margin-bottom: 1rem;
    color: #7f8c8d;
}

.logs-table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.logs-table {
    margin: 0;
}

.logs-table th {
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}

.timestamp {
    min-width: 120px;
}

.datetime {
    font-weight: 500;
    color: #2c3e50;
}

.time {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.ip-address code {
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.method-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.method-get { background: #d4edda; color: #155724; }
.method-post { background: #cce5ff; color: #004085; }
.method-put { background: #fff3cd; color: #856404; }
.method-delete { background: #f8d7da; color: #721c24; }

.endpoint code {
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #7f8c8d;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 800px;
    max-height: 80vh;
    width: 90%;
    overflow: hidden;
}

.modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.pagination-container {
    margin-top: 1.5rem;
    text-align: center;
}

@media (max-width: 768px) {
    .filter-row {
        grid-template-columns: 1fr;
    }
    
    .filter-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-actions .btn {
        margin-bottom: 0.5rem;
    }
}
</style>

<script>
// Log details modal functionality
function viewLogDetails(logId) {
    const modal = document.getElementById('logModal');
    const modalBody = document.getElementById('logModalBody');
    
    // Show loading state
    modalBody.innerHTML = '<div class="loading"><div class="spinner"></div><p>Loading log details...</p></div>';
    modal.style.display = 'flex';
    
    // Fetch log details
    fetch(`/admin/logs/${logId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                modalBody.innerHTML = formatLogDetails(data.log);
            } else {
                modalBody.innerHTML = '<div class="alert alert-error">Failed to load log details.</div>';
            }
        })
        .catch(error => {
            modalBody.innerHTML = '<div class="alert alert-error">Error loading log details.</div>';
        });
}

function closeLogModal() {
    document.getElementById('logModal').style.display = 'none';
}

function formatLogDetails(log) {
    return `
        <div class="log-details">
            <div class="detail-section">
                <h4>Request Information</h4>
                <table class="detail-table">
                    <tr><td><strong>Timestamp:</strong></td><td>${log.created_at}</td></tr>
                    <tr><td><strong>IP Address:</strong></td><td><code>${log.ip_address}</code></td></tr>
                    <tr><td><strong>Method:</strong></td><td>${log.method}</td></tr>
                    <tr><td><strong>Endpoint:</strong></td><td><code>${log.endpoint}</code></td></tr>
                    <tr><td><strong>Status Code:</strong></td><td>${log.response_status}</td></tr>
                    <tr><td><strong>Processing Time:</strong></td><td>${log.processing_time}ms</td></tr>
                </table>
            </div>
            
            <div class="detail-section">
                <h4>Request Data</h4>
                <pre class="json-data">${JSON.stringify(log.request_data, null, 2)}</pre>
            </div>
            
            <div class="detail-section">
                <h4>Response Data</h4>
                <pre class="json-data">${JSON.stringify(log.response_data, null, 2)}</pre>
            </div>
        </div>
    `;
}

// Export functionality
document.getElementById('exportBtn').addEventListener('click', function() {
    const form = document.getElementById('filtersForm');
    const formData = new FormData(form);
    formData.append('export', 'csv');
    
    // Create a temporary form for export
    const exportForm = document.createElement('form');
    exportForm.method = 'GET';
    exportForm.action = '/admin/logs/export';
    
    for (const [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        exportForm.appendChild(input);
    }
    
    document.body.appendChild(exportForm);
    exportForm.submit();
    document.body.removeChild(exportForm);
});

// Close modal when clicking outside
document.getElementById('logModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLogModal();
    }
});

// Auto-submit form on filter change (with debounce)
let filterTimeout;
document.querySelectorAll('#filtersForm select, #filtersForm input[type="date"]').forEach(element => {
    element.addEventListener('change', function() {
        clearTimeout(filterTimeout);
        filterTimeout = setTimeout(() => {
            document.getElementById('filtersForm').submit();
        }, 300);
    });
});

// Search input with debounce
document.getElementById('search').addEventListener('input', function() {
    clearTimeout(filterTimeout);
    filterTimeout = setTimeout(() => {
        document.getElementById('filtersForm').submit();
    }, 1000);
});

// Add CSS for detail table and JSON data
const additionalCSS = `
.detail-section {
    margin-bottom: 1.5rem;
}

.detail-section h4 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.detail-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.detail-table td {
    padding: 0.5rem;
    border-bottom: 1px solid #eee;
    vertical-align: top;
}

.detail-table td:first-child {
    width: 150px;
    background: #f8f9fa;
}

.json-data {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.9rem;
    max-height: 300px;
    overflow-y: auto;
}
`;

const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);
</script>
