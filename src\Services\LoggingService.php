<?php

namespace Skpassegna\GuardgeoApi\Services;

use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\JsonFormatter;
use Skpassegna\GuardgeoApi\Config\Database as DatabaseConfig;
use Skpassegna\GuardgeoApi\Models\ApiLog;

/**
 * LoggingService Class
 * 
 * Provides structured logging functionality for the GuardGeo API Platform.
 * Handles API requests, webhook events, admin actions, and error logging.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class LoggingService
{
    /**
     * @var Logger Main logger instance
     */
    private Logger $logger;

    /**
     * @var Logger API logger instance
     */
    private Logger $apiLogger;

    /**
     * @var Logger Webhook logger instance
     */
    private Logger $webhookLogger;

    /**
     * @var Logger Admin logger instance
     */
    private Logger $adminLogger;

    /**
     * @var array Application configuration
     */
    private array $config;

    /**
     * @var ApiLog API log model instance
     */
    private ApiLog $apiLogModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->config = DatabaseConfig::getAppConfig();
        $this->apiLogModel = new ApiLog();
        $this->initializeLoggers();
    }

    /**
     * Initialize all logger instances
     * 
     * @return void
     */
    private function initializeLoggers(): void
    {
        $logPath = $this->config['log_path'];
        $logLevel = $this->getLogLevel($this->config['log_level']);

        // Main application logger
        $this->logger = new Logger('guardgeo_api');
        $this->logger->pushHandler(
            new RotatingFileHandler($logPath . 'application.log', 30, $logLevel)
        );

        // API request logger
        $this->apiLogger = new Logger('api');
        $apiHandler = new RotatingFileHandler($logPath . 'api.log', 30, Logger::INFO);
        $apiHandler->setFormatter(new JsonFormatter());
        $this->apiLogger->pushHandler($apiHandler);

        // Webhook logger
        $this->webhookLogger = new Logger('webhook');
        $webhookHandler = new RotatingFileHandler($logPath . 'webhook.log', 30, Logger::INFO);
        $webhookHandler->setFormatter(new JsonFormatter());
        $this->webhookLogger->pushHandler($webhookHandler);

        // Admin action logger
        $this->adminLogger = new Logger('admin');
        $adminHandler = new RotatingFileHandler($logPath . 'admin.log', 30, Logger::INFO);
        $adminHandler->setFormatter(new JsonFormatter());
        $this->adminLogger->pushHandler($adminHandler);

        // Error logger
        if (defined('DEBUG') && DEBUG) {
            $errorHandler = new StreamHandler($logPath . 'error.log', Logger::DEBUG);
            $this->logger->pushHandler($errorHandler);
        }
    }

    /**
     * Get Monolog log level from string
     * 
     * @param string $level Log level string
     * @return int Monolog log level
     */
    private function getLogLevel(string $level): int
    {
        switch (strtolower($level)) {
            case 'debug':
                return Logger::DEBUG;
            case 'info':
                return Logger::INFO;
            case 'warning':
            case 'warn':
                return Logger::WARNING;
            case 'error':
                return Logger::ERROR;
            case 'critical':
                return Logger::CRITICAL;
            default:
                return Logger::INFO;
        }
    }

    /**
     * Log API request
     * 
     * @param string $method HTTP method
     * @param string $endpoint API endpoint
     * @param string $ipAddress Client IP address
     * @param array|null $requestData Request data
     * @param int $responseStatus HTTP response status
     * @param array|null $responseData Response data
     * @param int $processingTime Processing time in milliseconds
     * @param string|null $requestId Request ID
     * @return void
     */
    public function logApiRequest(
        string $method,
        string $endpoint,
        string $ipAddress,
        ?array $requestData = null,
        int $responseStatus = 200,
        ?array $responseData = null,
        int $processingTime = 0,
        ?string $requestId = null
    ): void {
        $context = [
            'method' => $method,
            'endpoint' => $endpoint,
            'ip_address' => $ipAddress,
            'request_data' => $requestData,
            'response_status' => $responseStatus,
            'response_data' => $responseData,
            'processing_time' => $processingTime,
            'request_id' => $requestId,
            'timestamp' => date('c'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];

        $message = "API Request: {$method} {$endpoint} - Status: {$responseStatus}";

        if ($responseStatus >= 400) {
            $this->apiLogger->error($message, $context);
        } else {
            $this->apiLogger->info($message, $context);
        }

        // Also store in database for analytics and monitoring
        try {
            $this->apiLogModel->logRequest(
                $method,
                $endpoint,
                $ipAddress,
                $requestData,
                $responseStatus,
                $responseData,
                $processingTime
            );
        } catch (\Exception $e) {
            // Log database error but don't fail the request
            error_log("Failed to log API request to database: " . $e->getMessage());
        }
    }

    /**
     * Log webhook event
     * 
     * @param string $source Webhook source (e.g., 'freemius')
     * @param string $event Event type
     * @param string|null $payload Raw payload
     * @param int $responseStatus HTTP response status
     * @param string|null $requestId Request ID
     * @param string|null $errorMessage Error message if any
     * @return void
     */
    public function logWebhook(
        string $source,
        string $event,
        ?string $payload = null,
        int $responseStatus = 200,
        ?string $requestId = null,
        ?string $errorMessage = null
    ): void {
        $context = [
            'source' => $source,
            'event' => $event,
            'payload_size' => $payload ? strlen($payload) : 0,
            'response_status' => $responseStatus,
            'request_id' => $requestId,
            'error_message' => $errorMessage,
            'timestamp' => date('c'),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null
        ];

        // Only log payload in debug mode
        if (defined('DEBUG') && DEBUG && $payload) {
            $context['payload'] = $payload;
        }

        $message = "Webhook: {$source} - Event: {$event} - Status: {$responseStatus}";
        
        if ($responseStatus >= 400) {
            $this->webhookLogger->error($message, $context);
        } else {
            $this->webhookLogger->info($message, $context);
        }
    }

    /**
     * Log admin action
     * 
     * @param string $action Action performed
     * @param string|null $userEmail Admin user email
     * @param array|null $details Action details
     * @param string|null $ipAddress IP address
     * @return void
     */
    public function logAdminAction(
        string $action,
        ?string $userEmail = null,
        ?array $details = null,
        ?string $ipAddress = null
    ): void {
        $context = [
            'action' => $action,
            'user_email' => $userEmail,
            'details' => $details,
            'ip_address' => $ipAddress ?? $_SERVER['REMOTE_ADDR'] ?? null,
            'timestamp' => date('c'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];

        $message = "Admin Action: {$action}";
        if ($userEmail) {
            $message .= " by {$userEmail}";
        }

        $this->adminLogger->info($message, $context);
    }

    /**
     * Log general application event
     * 
     * @param string $level Log level (debug, info, warning, error, critical)
     * @param string $message Log message
     * @param array $context Additional context
     * @return void
     */
    public function log(string $level, string $message, array $context = []): void
    {
        $context['timestamp'] = date('c');
        
        switch (strtolower($level)) {
            case 'debug':
                $this->logger->debug($message, $context);
                break;
            case 'info':
                $this->logger->info($message, $context);
                break;
            case 'warning':
            case 'warn':
                $this->logger->warning($message, $context);
                break;
            case 'error':
                $this->logger->error($message, $context);
                break;
            case 'critical':
                $this->logger->critical($message, $context);
                break;
            default:
                $this->logger->info($message, $context);
        }
    }

    /**
     * Log error with exception details
     * 
     * @param string $message Error message
     * @param \Exception|\Throwable $exception Exception instance
     * @param array $context Additional context
     * @return void
     */
    public function logException(string $message, $exception, array $context = []): void
    {
        $context = array_merge($context, [
            'exception_class' => get_class($exception),
            'exception_message' => $exception->getMessage(),
            'exception_code' => $exception->getCode(),
            'exception_file' => $exception->getFile(),
            'exception_line' => $exception->getLine(),
            'exception_trace' => $exception->getTraceAsString(),
            'timestamp' => date('c')
        ]);

        $this->logger->error($message, $context);
    }

    /**
     * Log security event
     * 
     * @param string $event Security event type
     * @param string $description Event description
     * @param array $context Additional context
     * @return void
     */
    public function logSecurityEvent(string $event, string $description, array $context = []): void
    {
        $context = array_merge($context, [
            'security_event' => $event,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'timestamp' => date('c')
        ]);

        $message = "Security Event: {$event} - {$description}";
        $this->logger->warning($message, $context);
    }

    /**
     * Get logger instance
     * 
     * @param string $type Logger type (main, api, webhook, admin)
     * @return Logger Logger instance
     */
    public function getLogger(string $type = 'main'): Logger
    {
        switch ($type) {
            case 'api':
                return $this->apiLogger;
            case 'webhook':
                return $this->webhookLogger;
            case 'admin':
                return $this->adminLogger;
            default:
                return $this->logger;
        }
    }
}
