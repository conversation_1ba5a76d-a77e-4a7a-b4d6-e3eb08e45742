<?php

namespace Skpassegna\GuardgeoApi\Models;

use PDOException;
use Skpassegna\GuardgeoApi\Core\Database;

/**
 * BaseModel Class
 * 
 * Base class for all data models in the GuardGeo API Platform.
 * Provides common CRUD operations and database interaction methods.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
abstract class BaseModel
{
    /**
     * @var string Table name
     */
    protected string $table;

    /**
     * @var string Primary key column name
     */
    protected string $primaryKey = 'id';

    /**
     * @var array Fillable columns for mass assignment
     */
    protected array $fillable = [];

    /**
     * @var array Columns that should be cast to specific types
     */
    protected array $casts = [];

    /**
     * @var bool Whether the model uses timestamps
     */
    protected bool $timestamps = true;

    /**
     * Constructor
     */
    public function __construct()
    {
        if (empty($this->table)) {
            throw new \Exception('Table name must be defined in model');
        }
    }

    /**
     * Find a record by ID
     * 
     * @param int $id Record ID
     * @return array|null Record data or null if not found
     */
    public function findById(int $id): ?array
    {
        try {
            $query = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id LIMIT 1";
            $result = Database::queryOne($query, ['id' => $id]);
            
            return $result ? $this->castAttributes($result) : null;
            
        } catch (PDOException $e) {
            error_log("Error finding record by ID: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Find records by criteria
     * 
     * @param array $criteria Search criteria
     * @param array $options Query options (limit, offset, order)
     * @return array Array of records
     */
    public function findBy(array $criteria, array $options = []): array
    {
        try {
            $whereClause = $this->buildWhereClause($criteria);
            $query = "SELECT * FROM {$this->table}";
            
            if (!empty($whereClause['clause'])) {
                $query .= " WHERE " . $whereClause['clause'];
            }
            
            // Add ordering
            if (isset($options['order'])) {
                $query .= " ORDER BY " . $options['order'];
            }
            
            // Add limit and offset
            if (isset($options['limit'])) {
                $query .= " LIMIT " . (int)$options['limit'];
                
                if (isset($options['offset'])) {
                    $query .= " OFFSET " . (int)$options['offset'];
                }
            }
            
            $results = Database::query($query, $whereClause['params']);
            
            return array_map([$this, 'castAttributes'], $results);
            
        } catch (PDOException $e) {
            error_log("Error finding records: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Find a single record by criteria
     * 
     * @param array $criteria Search criteria
     * @return array|null Record data or null if not found
     */
    public function findOneBy(array $criteria): ?array
    {
        $results = $this->findBy($criteria, ['limit' => 1]);
        return !empty($results) ? $results[0] : null;
    }

    /**
     * Create a new record
     * 
     * @param array $data Record data
     * @return int|null Created record ID or null on failure
     */
    public function create(array $data): ?int
    {
        try {
            $data = $this->filterFillable($data);
            
            if ($this->timestamps) {
                $data['created_at'] = date('Y-m-d H:i:s');
                $data['updated_at'] = date('Y-m-d H:i:s');
            }
            
            $columns = array_keys($data);
            $placeholders = array_map(fn($col) => ":$col", $columns);
            
            $query = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") 
                      VALUES (" . implode(', ', $placeholders) . ")";
            
            Database::execute($query, $data);
            
            return (int)Database::lastInsertId($this->table . '_' . $this->primaryKey . '_seq');
            
        } catch (PDOException $e) {
            error_log("Error creating record: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Update a record by ID
     * 
     * @param int $id Record ID
     * @param array $data Update data
     * @return bool True on success
     */
    public function updateById(int $id, array $data): bool
    {
        try {
            $data = $this->filterFillable($data);
            
            if ($this->timestamps) {
                $data['updated_at'] = date('Y-m-d H:i:s');
            }
            
            $setParts = array_map(fn($col) => "$col = :$col", array_keys($data));
            $data['id'] = $id;
            
            $query = "UPDATE {$this->table} SET " . implode(', ', $setParts) . 
                     " WHERE {$this->primaryKey} = :id";
            
            return Database::execute($query, $data) > 0;
            
        } catch (PDOException $e) {
            error_log("Error updating record: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a record by ID
     * 
     * @param int $id Record ID
     * @return bool True on success
     */
    public function deleteById(int $id): bool
    {
        try {
            $query = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = :id";
            return Database::execute($query, ['id' => $id]) > 0;
            
        } catch (PDOException $e) {
            error_log("Error deleting record: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Count records by criteria
     * 
     * @param array $criteria Search criteria
     * @return int Record count
     */
    public function countBy(array $criteria = []): int
    {
        try {
            $whereClause = $this->buildWhereClause($criteria);
            $query = "SELECT COUNT(*) as count FROM {$this->table}";
            
            if (!empty($whereClause['clause'])) {
                $query .= " WHERE " . $whereClause['clause'];
            }
            
            $result = Database::queryOne($query, $whereClause['params']);
            
            return (int)($result['count'] ?? 0);
            
        } catch (PDOException $e) {
            error_log("Error counting records: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Check if a record exists by criteria
     * 
     * @param array $criteria Search criteria
     * @return bool True if record exists
     */
    public function exists(array $criteria): bool
    {
        return $this->countBy($criteria) > 0;
    }

    /**
     * Build WHERE clause from criteria
     * 
     * @param array $criteria Search criteria
     * @return array WHERE clause and parameters
     */
    protected function buildWhereClause(array $criteria): array
    {
        if (empty($criteria)) {
            return ['clause' => '', 'params' => []];
        }
        
        $conditions = [];
        $params = [];
        
        foreach ($criteria as $column => $value) {
            if (is_array($value)) {
                // Handle IN clause
                $placeholders = [];
                foreach ($value as $i => $val) {
                    $placeholder = $column . '_' . $i;
                    $placeholders[] = ':' . $placeholder;
                    $params[$placeholder] = $val;
                }
                $conditions[] = "$column IN (" . implode(', ', $placeholders) . ")";
            } else {
                $conditions[] = "$column = :$column";
                $params[$column] = $value;
            }
        }
        
        return [
            'clause' => implode(' AND ', $conditions),
            'params' => $params
        ];
    }

    /**
     * Filter data to only include fillable columns
     * 
     * @param array $data Input data
     * @return array Filtered data
     */
    protected function filterFillable(array $data): array
    {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }

    /**
     * Cast attributes to their specified types
     * 
     * @param array $attributes Raw attributes
     * @return array Casted attributes
     */
    protected function castAttributes(array $attributes): array
    {
        foreach ($this->casts as $key => $type) {
            if (isset($attributes[$key])) {
                $attributes[$key] = $this->castAttribute($attributes[$key], $type);
            }
        }
        
        return $attributes;
    }

    /**
     * Cast a single attribute to its specified type
     * 
     * @param mixed $value Attribute value
     * @param string $type Cast type
     * @return mixed Casted value
     */
    protected function castAttribute($value, string $type)
    {
        if ($value === null) {
            return null;
        }
        
        switch ($type) {
            case 'int':
            case 'integer':
                return (int)$value;
            case 'float':
            case 'double':
                return (float)$value;
            case 'bool':
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'string':
                return (string)$value;
            case 'array':
            case 'json':
                return is_string($value) ? json_decode($value, true) : $value;
            case 'datetime':
                return $value instanceof \DateTime ? $value : new \DateTime($value);
            default:
                return $value;
        }
    }

    /**
     * Get table name
     * 
     * @return string Table name
     */
    public function getTable(): string
    {
        return $this->table;
    }

    /**
     * Get primary key column name
     * 
     * @return string Primary key column name
     */
    public function getPrimaryKey(): string
    {
        return $this->primaryKey;
    }
}
