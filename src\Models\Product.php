<?php

namespace Skpassegna\GuardgeoApi\Models;

/**
 * Product Model
 * 
 * Manages Freemius product data including plugins, themes, and other products.
 * Handles product validation, webhook updates, and status checking.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class Product extends BaseModel
{
    /**
     * @var string Table name
     */
    protected string $table = 'products';

    /**
     * @var array Fillable columns for mass assignment
     */
    protected array $fillable = [
        'freemius_id',
        'secret_key',
        'public_key',
        'parent_plugin_id',
        'developer_id',
        'store_id',
        'slug',
        'title',
        'environment',
        'icon',
        'default_plan_id',
        'plans',
        'features',
        'money_back_period',
        'refund_policy',
        'annual_renewals_discount',
        'renewals_discount_type',
        'is_released',
        'is_sdk_required',
        'is_pricing_visible',
        'is_wp_org_compliant',
        'installs_count',
        'active_installs_count',
        'free_releases_count',
        'premium_releases_count',
        'total_purchases',
        'total_subscriptions',
        'total_renewals',
        'total_failed_purchases',
        'earnings',
        'type',
        'is_static'
    ];

    /**
     * @var array Columns that should be cast to specific types
     */
    protected array $casts = [
        'freemius_id' => 'int',
        'parent_plugin_id' => 'int',
        'developer_id' => 'int',
        'store_id' => 'int',
        'environment' => 'int',
        'default_plan_id' => 'int',
        'money_back_period' => 'int',
        'annual_renewals_discount' => 'int',
        'is_released' => 'bool',
        'is_sdk_required' => 'bool',
        'is_pricing_visible' => 'bool',
        'is_wp_org_compliant' => 'bool',
        'installs_count' => 'int',
        'active_installs_count' => 'int',
        'free_releases_count' => 'int',
        'premium_releases_count' => 'int',
        'total_purchases' => 'int',
        'total_subscriptions' => 'int',
        'total_renewals' => 'int',
        'total_failed_purchases' => 'int',
        'earnings' => 'float',
        'is_static' => 'bool'
    ];

    /**
     * Find product by Freemius ID
     * 
     * @param int $freemiusId Freemius product ID
     * @return array|null Product data or null if not found
     */
    public function findByFreemiusId(int $freemiusId): ?array
    {
        return $this->findOneBy(['freemius_id' => $freemiusId]);
    }

    /**
     * Find product by slug
     * 
     * @param string $slug Product slug
     * @return array|null Product data or null if not found
     */
    public function findBySlug(string $slug): ?array
    {
        return $this->findOneBy(['slug' => $slug]);
    }

    /**
     * Check if product is active and released
     * 
     * @param int $freemiusId Freemius product ID
     * @return bool True if product is active
     */
    public function isActive(int $freemiusId): bool
    {
        $product = $this->findByFreemiusId($freemiusId);
        
        if (!$product) {
            return false;
        }
        
        return $product['is_released'] === true;
    }

    /**
     * Check if product is in production environment
     * 
     * @param int $freemiusId Freemius product ID
     * @return bool True if in production
     */
    public function isProduction(int $freemiusId): bool
    {
        $product = $this->findByFreemiusId($freemiusId);
        
        if (!$product) {
            return false;
        }
        
        return $product['environment'] === 0; // 0 = production
    }

    /**
     * Get all active products
     * 
     * @return array Array of active products
     */
    public function getActiveProducts(): array
    {
        return $this->findBy(['is_released' => true], ['order' => 'title ASC']);
    }

    /**
     * Update product from Freemius webhook data
     * 
     * @param array $webhookData Freemius webhook data
     * @return bool True on success
     */
    public function updateFromWebhook(array $webhookData): bool
    {
        if (!isset($webhookData['id'])) {
            return false;
        }
        
        $freemiusId = (int)$webhookData['id'];
        $existingProduct = $this->findByFreemiusId($freemiusId);
        
        $productData = $this->mapWebhookData($webhookData);
        
        if ($existingProduct) {
            // Update existing product
            return $this->updateById($existingProduct['id'], $productData);
        } else {
            // Create new product
            $productData['freemius_id'] = $freemiusId;
            return $this->create($productData) !== null;
        }
    }

    /**
     * Map Freemius webhook data to product fields
     *
     * @param array $webhookData Freemius webhook data
     * @return array Mapped product data
     */
    protected function mapWebhookData(array $webhookData): array
    {
        $mapped = [];

        // Direct field mappings (Freemius field => Database field)
        $fieldMappings = [
            'secret_key' => 'secret_key',
            'public_key' => 'public_key',
            'parent_plugin_id' => 'parent_plugin_id',
            'developer_id' => 'developer_id',
            'store_id' => 'store_id',
            'slug' => 'slug',
            'title' => 'title',
            'environment' => 'environment',
            'icon' => 'icon',
            'default_plan_id' => 'default_plan_id',
            'money_back_period' => 'money_back_period',
            'refund_policy' => 'refund_policy',
            'annual_renewals_discount' => 'annual_renewals_discount',
            'renewals_discount_type' => 'renewals_discount_type',
            'is_released' => 'is_released',
            'is_sdk_required' => 'is_sdk_required',
            'is_pricing_visible' => 'is_pricing_visible',
            'is_wp_org_compliant' => 'is_wp_org_compliant',
            'installs_count' => 'installs_count',
            'active_installs_count' => 'active_installs_count',
            'free_releases_count' => 'free_releases_count',
            'premium_releases_count' => 'premium_releases_count',
            'total_purchases' => 'total_purchases',
            'total_subscriptions' => 'total_subscriptions',
            'total_renewals' => 'total_renewals',
            'total_failed_purchases' => 'total_failed_purchases',
            'earnings' => 'earnings',
            'type' => 'type',
            'is_static' => 'is_static'
        ];

        foreach ($fieldMappings as $webhookField => $dbField) {
            if (isset($webhookData[$webhookField])) {
                $value = $webhookData[$webhookField];

                // Handle type conversions for fields that come as strings but should be integers
                if (in_array($dbField, ['parent_plugin_id', 'developer_id', 'store_id', 'default_plan_id']) && is_string($value)) {
                    $value = $value !== null ? (int)$value : null;
                }

                // Handle earnings as float (can come as string, int, or float)
                if ($dbField === 'earnings' && !is_null($value)) {
                    $value = (float)$value;
                }

                // Handle total_failed_purchases as integer (comes as string in API)
                if ($dbField === 'total_failed_purchases' && is_string($value)) {
                    $value = (int)$value;
                }

                $mapped[$dbField] = $value;
            }
        }

        // Handle plans field - can be string or array
        if (isset($webhookData['plans'])) {
            if (is_array($webhookData['plans'])) {
                $mapped['plans'] = implode(',', array_column($webhookData['plans'], 'id'));
            } else {
                $mapped['plans'] = $webhookData['plans']; // Already a comma-separated string
            }
        }

        // Handle features field - can be string or array
        if (isset($webhookData['features'])) {
            if (is_array($webhookData['features'])) {
                $mapped['features'] = implode(',', array_column($webhookData['features'], 'id'));
            } else {
                $mapped['features'] = $webhookData['features']; // Already a comma-separated string
            }
        }

        // Handle timestamp mapping (Freemius uses 'created' and 'updated', we use 'created_at' and 'updated_at')
        if (isset($webhookData['created'])) {
            $mapped['created_at'] = $webhookData['created'];
        }

        if (isset($webhookData['updated'])) {
            $mapped['updated_at'] = $webhookData['updated'];
        }

        return $mapped;
    }

    /**
     * Get product statistics
     * 
     * @param int $freemiusId Freemius product ID
     * @return array Product statistics
     */
    public function getStatistics(int $freemiusId): array
    {
        $product = $this->findByFreemiusId($freemiusId);
        
        if (!$product) {
            return [];
        }
        
        return [
            'installs_count' => $product['installs_count'],
            'active_installs_count' => $product['active_installs_count'],
            'total_purchases' => $product['total_purchases'],
            'total_subscriptions' => $product['total_subscriptions'],
            'total_renewals' => $product['total_renewals'],
            'earnings' => $product['earnings'],
            'conversion_rate' => $product['installs_count'] > 0 
                ? round(($product['total_purchases'] / $product['installs_count']) * 100, 2)
                : 0
        ];
    }

    /**
     * Get product plans as array
     * 
     * @param int $freemiusId Freemius product ID
     * @return array Plan IDs
     */
    public function getPlans(int $freemiusId): array
    {
        $product = $this->findByFreemiusId($freemiusId);
        
        if (!$product || empty($product['plans'])) {
            return [];
        }
        
        return array_map('intval', explode(',', $product['plans']));
    }

    /**
     * Get product features as array
     * 
     * @param int $freemiusId Freemius product ID
     * @return array Feature IDs
     */
    public function getFeatures(int $freemiusId): array
    {
        $product = $this->findByFreemiusId($freemiusId);
        
        if (!$product || empty($product['features'])) {
            return [];
        }
        
        return array_map('intval', explode(',', $product['features']));
    }

    /**
     * Validate product for API access
     *
     * @param int $freemiusId Freemius product ID
     * @return array Validation result
     */
    public function validateForApiAccess(int $freemiusId): array
    {
        $product = $this->findByFreemiusId($freemiusId);

        if (!$product) {
            return [
                'valid' => false,
                'reason' => 'Product not found',
                'code' => 'PRODUCT_NOT_FOUND'
            ];
        }

        if (!$product['is_released']) {
            return [
                'valid' => false,
                'reason' => 'Product not released',
                'code' => 'PRODUCT_NOT_RELEASED'
            ];
        }

        if ($product['environment'] !== 0) {
            return [
                'valid' => false,
                'reason' => 'Product not in production environment',
                'code' => 'PRODUCT_NOT_PRODUCTION'
            ];
        }

        return [
            'valid' => true,
            'product' => $product
        ];
    }
}
