<?php

namespace Skpassegna\GuardgeoApi\Models;

use PDOException;
use Skpassegna\GuardgeoApi\Core\Database;

/**
 * User Model Class
 * 
 * Manages admin users with role-based access control for the GuardGeo API Platform.
 * Handles authentication, authorization, and user management operations.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class User extends BaseModel
{
    /**
     * @var string Table name
     */
    protected string $table = 'admin_users';

    /**
     * @var array Fillable columns for mass assignment
     */
    protected array $fillable = [
        'email',
        'password_hash',
        'role',
        'is_active',
        'last_login'
    ];

    /**
     * @var array Columns that should be cast to specific types
     */
    protected array $casts = [
        'is_active' => 'boolean',
        'last_login' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * User roles constants
     */
    public const ROLE_SUPER_ADMIN = 'super_admin';
    public const ROLE_DEV = 'dev';
    public const ROLE_MARKETING = 'marketing';
    public const ROLE_SALES = 'sales';

    /**
     * Valid user roles
     */
    public const VALID_ROLES = [
        self::ROLE_SUPER_ADMIN,
        self::ROLE_DEV,
        self::ROLE_MARKETING,
        self::ROLE_SALES
    ];

    /**
     * Role permissions mapping
     */
    public const ROLE_PERMISSIONS = [
        self::ROLE_SUPER_ADMIN => [
            'user_management',
            'system_settings',
            'view_logs',
            'manual_ip_lookup',
            'freemius_sync',
            'dashboard_access',
            'technical_tools',
            'marketing_analytics',
            'sales_analytics'
        ],
        self::ROLE_DEV => [
            'view_logs',
            'manual_ip_lookup',
            'freemius_sync',
            'dashboard_access',
            'technical_tools'
        ],
        self::ROLE_MARKETING => [
            'dashboard_access',
            'marketing_analytics',
            'view_logs'
        ],
        self::ROLE_SALES => [
            'dashboard_access',
            'sales_analytics',
            'view_logs'
        ]
    ];

    /**
     * Find user by email address
     * 
     * @param string $email User email
     * @return array|null User data or null if not found
     */
    public function findByEmail(string $email): ?array
    {
        return $this->findOneBy(['email' => $email]);
    }

    /**
     * Find active user by email address
     * 
     * @param string $email User email
     * @return array|null User data or null if not found or inactive
     */
    public function findActiveByEmail(string $email): ?array
    {
        return $this->findOneBy(['email' => $email, 'is_active' => true]);
    }

    /**
     * Create a new user with password hashing
     * 
     * @param array $userData User data including plain password
     * @return int|null Created user ID or null on failure
     */
    public function createUser(array $userData): ?int
    {
        try {
            // Validate required fields
            if (empty($userData['email']) || empty($userData['password'])) {
                throw new \InvalidArgumentException('Email and password are required');
            }

            // Validate password strength
            if (!$this->isValidPassword($userData['password'])) {
                throw new \InvalidArgumentException('Password must be at least 12 characters long');
            }

            // Validate role
            $role = $userData['role'] ?? self::ROLE_DEV;
            if (!$this->isValidRole($role)) {
                throw new \InvalidArgumentException('Invalid user role');
            }

            // Check if email already exists
            if ($this->findByEmail($userData['email'])) {
                throw new \InvalidArgumentException('Email address already exists');
            }

            // Hash password
            $userData['password_hash'] = password_hash($userData['password'], PASSWORD_DEFAULT);
            unset($userData['password']);

            // Set default values
            $userData['role'] = $role;
            $userData['is_active'] = $userData['is_active'] ?? true;

            return $this->create($userData);

        } catch (\Exception $e) {
            error_log("Error creating user: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Update user password
     * 
     * @param int $userId User ID
     * @param string $newPassword New plain password
     * @return bool True on success
     */
    public function updatePassword(int $userId, string $newPassword): bool
    {
        try {
            if (!$this->isValidPassword($newPassword)) {
                throw new \InvalidArgumentException('Password must be at least 12 characters long');
            }

            $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
            
            return $this->updateById($userId, ['password_hash' => $passwordHash]);

        } catch (\Exception $e) {
            error_log("Error updating password: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update user's last login timestamp
     * 
     * @param int $userId User ID
     * @return bool True on success
     */
    public function updateLastLogin(int $userId): bool
    {
        return $this->updateById($userId, ['last_login' => date('Y-m-d H:i:s')]);
    }

    /**
     * Verify user password
     * 
     * @param string $password Plain password
     * @param string $hash Stored password hash
     * @return bool True if password matches
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * Check if user has specific permission
     * 
     * @param array $user User data
     * @param string $permission Permission to check
     * @return bool True if user has permission
     */
    public function hasPermission(array $user, string $permission): bool
    {
        $role = $user['role'] ?? '';
        $permissions = self::ROLE_PERMISSIONS[$role] ?? [];
        
        return in_array($permission, $permissions);
    }

    /**
     * Check if user can manage other users
     * 
     * @param array $user User data
     * @return bool True if user can manage users
     */
    public function canManageUsers(array $user): bool
    {
        return $this->hasPermission($user, 'user_management');
    }

    /**
     * Check if user can access technical tools
     * 
     * @param array $user User data
     * @return bool True if user can access technical tools
     */
    public function canAccessTechnicalTools(array $user): bool
    {
        return $this->hasPermission($user, 'technical_tools');
    }

    /**
     * Get all active users
     * 
     * @return array Array of active users
     */
    public function getActiveUsers(): array
    {
        return $this->findBy(['is_active' => true], ['order' => 'email ASC']);
    }

    /**
     * Get users by role
     * 
     * @param string $role User role
     * @return array Array of users with specified role
     */
    public function getUsersByRole(string $role): array
    {
        if (!$this->isValidRole($role)) {
            return [];
        }

        return $this->findBy(['role' => $role, 'is_active' => true], ['order' => 'email ASC']);
    }

    /**
     * Deactivate user account
     * 
     * @param int $userId User ID
     * @return bool True on success
     */
    public function deactivateUser(int $userId): bool
    {
        return $this->updateById($userId, ['is_active' => false]);
    }

    /**
     * Activate user account
     * 
     * @param int $userId User ID
     * @return bool True on success
     */
    public function activateUser(int $userId): bool
    {
        return $this->updateById($userId, ['is_active' => true]);
    }

    /**
     * Validate password strength
     * 
     * @param string $password Password to validate
     * @return bool True if password meets requirements
     */
    private function isValidPassword(string $password): bool
    {
        return strlen($password) >= 12;
    }

    /**
     * Validate user role
     * 
     * @param string $role Role to validate
     * @return bool True if role is valid
     */
    private function isValidRole(string $role): bool
    {
        return in_array($role, self::VALID_ROLES);
    }

    /**
     * Get user role display name
     * 
     * @param string $role User role
     * @return string Display name for role
     */
    public function getRoleDisplayName(string $role): string
    {
        $displayNames = [
            self::ROLE_SUPER_ADMIN => 'Super Administrator',
            self::ROLE_DEV => 'Developer',
            self::ROLE_MARKETING => 'Marketing',
            self::ROLE_SALES => 'Sales'
        ];

        return $displayNames[$role] ?? 'Unknown';
    }

    /**
     * Get all available roles
     * 
     * @return array Array of role => display_name pairs
     */
    public function getAvailableRoles(): array
    {
        $roles = [];
        foreach (self::VALID_ROLES as $role) {
            $roles[$role] = $this->getRoleDisplayName($role);
        }
        return $roles;
    }
}
