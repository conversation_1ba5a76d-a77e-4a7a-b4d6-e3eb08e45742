<?php

namespace Skpassegna\GuardgeoApi\Core;

/**
 * Session Manager Class
 * 
 * Handles secure session configuration and management for the GuardGeo API Platform.
 * Provides secure session settings and session lifecycle management.
 * 
 * @package GuardGeo API Platform
 * <AUTHOR>
 */
class SessionManager
{
    /**
     * Initialize secure session configuration
     * 
     * @return void
     */
    public static function initialize(): void
    {
        // Only initialize if session hasn't been started
        if (session_status() === PHP_SESSION_NONE) {
            // Set secure session configuration
            ini_set('session.cookie_httponly', '1');
            ini_set('session.cookie_secure', '1'); // Enable in production with HTTPS
            ini_set('session.use_only_cookies', '1');
            ini_set('session.cookie_samesite', 'Strict');
            ini_set('session.gc_maxlifetime', '3600'); // 1 hour
            ini_set('session.cookie_lifetime', '3600'); // 1 hour
            
            // Set session name
            session_name('GUARDGEO_ADMIN_SESSION');
            
            // Start session
            session_start();
        }
    }

    /**
     * Regenerate session ID for security
     * 
     * @return bool True on success
     */
    public static function regenerateId(): bool
    {
        if (session_status() === PHP_SESSION_ACTIVE) {
            return session_regenerate_id(true);
        }
        return false;
    }

    /**
     * Destroy current session
     * 
     * @return bool True on success
     */
    public static function destroy(): bool
    {
        try {
            if (session_status() === PHP_SESSION_ACTIVE) {
                // Clear session data
                $_SESSION = [];

                // Delete session cookie
                if (ini_get("session.use_cookies")) {
                    $params = session_get_cookie_params();
                    setcookie(session_name(), '', time() - 42000,
                        $params["path"], $params["domain"],
                        $params["secure"], $params["httponly"]
                    );
                }

                // Destroy session
                return session_destroy();
            }
            return true;

        } catch (\Exception $e) {
            error_log("Session destruction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if session is active
     * 
     * @return bool True if session is active
     */
    public static function isActive(): bool
    {
        return session_status() === PHP_SESSION_ACTIVE;
    }

    /**
     * Get session ID
     * 
     * @return string Session ID or empty string if no session
     */
    public static function getId(): string
    {
        return session_id() ?: '';
    }

    /**
     * Set session variable
     * 
     * @param string $key Session key
     * @param mixed $value Session value
     * @return void
     */
    public static function set(string $key, $value): void
    {
        if (self::isActive()) {
            $_SESSION[$key] = $value;
        }
    }

    /**
     * Get session variable
     * 
     * @param string $key Session key
     * @param mixed $default Default value if key doesn't exist
     * @return mixed Session value or default
     */
    public static function get(string $key, $default = null)
    {
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Check if session variable exists
     * 
     * @param string $key Session key
     * @return bool True if key exists
     */
    public static function has(string $key): bool
    {
        return isset($_SESSION[$key]);
    }

    /**
     * Remove session variable
     * 
     * @param string $key Session key
     * @return void
     */
    public static function remove(string $key): void
    {
        unset($_SESSION[$key]);
    }

    /**
     * Clear all session data
     * 
     * @return void
     */
    public static function clear(): void
    {
        if (self::isActive()) {
            $_SESSION = [];
        }
    }

    /**
     * Get session configuration info
     * 
     * @return array Session configuration details
     */
    public static function getConfig(): array
    {
        return [
            'session_name' => session_name(),
            'session_id' => session_id(),
            'session_status' => session_status(),
            'cookie_lifetime' => ini_get('session.cookie_lifetime'),
            'gc_maxlifetime' => ini_get('session.gc_maxlifetime'),
            'cookie_httponly' => ini_get('session.cookie_httponly'),
            'cookie_secure' => ini_get('session.cookie_secure'),
            'use_only_cookies' => ini_get('session.use_only_cookies'),
        ];
    }
}
